return {
configurationEnableWindow = false,
enable = true,

xpEnableWindow = true,
xpHideWhenMenu = false,
xpHideWhenSymbolChat = false,
xpHideWhenMenuUnavailable = true,
xpShowDefaultNotError = true,
xpNoTitleBar = "NoTitleBar",
xpNoResize = "",
xpNoMove = "",
xpTransparent = false,
xpEnableInfoLevel = false,
xpEnableInfoTotal = false,
xpEnableInfoTNL = true,
xpBarNoOverlay = false,
xpBarColor = 0xFFE6B300,
xpBarPercentColor = 0xFFFFFFFF,
xpBarX = 245.000000,
xpBarY = -3.000000,
xpBarWidth = -1.000000,
xpBarHeight = 0.000000,
xpVerticalBar = false,
textwindow_enable = false,
textwindow_hideWhenMenuOpen = true,
textwindow_hideWhenSymbolChatOpen = false,
textwindow_hideWhenMenuNotAvailable = true,
textwindow_noTitleBar = "",
textwindow_noResize = "",
textwindow_noMove = "",
textwindow_transparent = false,
textwindow_x = 200.000000,
textwindow_y = 50.000000,
textwindow_percentColor = 0xFFFFFFFF,
}
