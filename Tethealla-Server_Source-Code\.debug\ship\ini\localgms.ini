#Local gm ini file
#
# Local gm key
#PLace the guild card number and rights level of each local GM
#here. An example is provided, if you decided to leave it
#<PERSON> will be a local gm. lol
[localgms]
42000043,3
#
# GM Rights key
#
#Place the rights level followed by the rights key
#For each rights level you want. you can from 0-9
#as the rights level. 0 is all player rights, so be careful
#
# Computing rights keys
#
# Each command has a number. Add all the values for the
# commands you want, then use that number as the key.
#
# /event         = 1
#                = 2
#                = 4
# /warpme        = 8
# /dc            = 16
# /dcall         = 32
# /annouce       = 64
# /levelup       = 128
# /updatelocalgms= 256
# /stfu		 = 512
# /warpall       = 1024
# /ban           = 2048
# /ipban         = 4096
# /hwban         = 4096
# /updatemasks   = 4096
#
# Simple, really. Some examples are included.
#
[gmrights]
# Someone who can change the ship event, warp themselves, disconnect
# and silence users, but that's it....
0,537
# Someone who can change the ship event, warp, disconnect a user, 
# level up, and silence a user
1,671
# Someone who can do everything but disconnect all users and reparse
# the local GM file.
2,1759
# Someone who's basically God (on the ship anyway)
3,32767