# Tethealla Server version 0.001 Configuration File 
#
# MySQL Host
localhost
# MySQL Username
mysqluser
# MySQL Password
mysqlpw
# MySQL Database
mysqldb
# MySQL Port
3306
# Server IP address to bind to (your private IP)
127.0.0.1
# Welcome message
Your welcome message here...
# Server Port (This is the port the login server listens on... it will also open port+1 for the character server... You shouldn't need to change this.)
12000
# Max number of client connections
100
# Max number of ships
50
# Override IP (your public IP, leave 0 to just use the binded IP)
0
# Rare monster appearance rate (% is basically the number divided by 1000)
#
# Hildebear
7
# Rappies
8
# Lillies
8
# Slimes
7
# Merissa
7
# Pazuzu
7
# Dorphon Eclair
7
# Kondrieu
10000
# Global GM name color
1D94F7
# Local GM name color
B0CEDE
# Normal name color
FFFFFF
