// Block packet tables (These help protect from FSOD)

//	SPSOF's old size_check table for lobbies.

unsigned short size_check_table [] =
{ 
	0x00, 0x00,
	0x01, 0x00,
	0x02, 0x00,
	0x03, 0x00,
	0x04, 0x00,
	0x05, 0x00,
	0x06, 0x98,
	0x07, 0x48,
	0x08, 0x00,
	0x09, 0x00,
	0x0A, 0x00,
	0x0B, 0x00,
	0x0C, 0x00,
	0x0D, 0x10,
	0x0E, 0x00,
	0x0F, 0x00,
	0x10, 0x00,
	0x11, 0x00,
	0x12, 0x00,
	0x13, 0x00,
	0x14, 0x00,
	0x15, 0x00,
	0x16, 0x00,
	0x17, 0x00,
	0x18, 0x00,
	0x19, 0x00,
	0x1A, 0x00,
	0x1B, 0x00,
	0x1C, 0x00,
	0x1D, 0x00,
	0x1E, 0x00,
	0x1F, 0x0C,
	0x20, 0x1C,
	0x21, 0x00,
	0x22, 0x08,
	0x23, 0x08,
	0x24, 0x00,
	0x25, 0x00,
	0x26, 0x00,
	0x27, 0x00,
	0x28, 0x00,
	0x29, 0x00,
	0x2A, 0x00,
	0x2B, 0x00,
	0x2C, 0x18,
	0x2D, 0x08,
	0x2E, 0x00,
	0x2F, 0x00,
	0x30, 0x00,
	0x31, 0x00,
	0x32, 0x00,
	0x33, 0x00,
	0x34, 0x00,
	0x35, 0x00,
	0x36, 0x00,
	0x37, 0x00,
	0x38, 0x00,
	0x39, 0x00,
	0x3A, 0x08,
	0x3B, 0x08,
	0x3C, 0x00,
	0x3D, 0x00,
	0x3E, 0x1C,
	0x3F, 0x1C,
	0x40, 0x14,
	0x41, 0x00,
	0x42, 0x10,
	0x43, 0x00,
	0x44, 0x00,
	0x45, 0x00,
	0x46, 0x00,
	0x47, 0x00,
	0x48, 0x00,
	0x49, 0x00,
	0x4A, 0x00,
	0x4B, 0x00,
	0x4C, 0x00,
	0x4D, 0x00,
	0x4E, 0x00,
	0x4F, 0x00,
	0x50, 0x00,
	0x51, 0x00,
	0x52, 0x10,
	0x53, 0x00,
	0x54, 0x00,
	0x55, 0x00,
	0x56, 0x00,
	0x57, 0x00,
	0x58, 0x0C,
	0x59, 0x00,
	0x5A, 0x00,
	0x5B, 0x00,
	0x5C, 0x00,
	0x5D, 0x00,
	0x5E, 0x00,
	0x5F, 0x00,
	0x60, 0x00,
	0x61, 0x00,
	0x62, 0x00,
	0x63, 0x00,
	0x64, 0x00,
	0x65, 0x00,
	0x66, 0x00,
	0x67, 0x00,
	0x68, 0x00,
	0x69, 0x00,
	0x6A, 0x00,
	0x6B, 0x00,
	0x6C, 0x00,
	0x6D, 0x00,
	0x6E, 0x00,
	0x6F, 0x20C,
	0x70, 0x00,
	0x71, 0x08,
	0x72, 0x00,
	0x73, 0x00,
	0x74, 0x24,
	0x75, 0x00,
	0x76, 0x00,
	0x77, 0x00,
	0x78, 0x00,
	0x79, 0x1C,
	0x7A, 0x00,
	0x7B, 0x00,
	0x7C, 0x00,
	0x7D, 0x00,
	0x7E, 0x00,
	0x7F, 0x00,
	0x80, 0x00,
	0x81, 0x00,
	0x82, 0x00,
	0x83, 0x00,
	0x84, 0x00,
	0x85, 0x00,
	0x86, 0x00,
	0x87, 0x00,
	0x88, 0x00,
	0x89, 0x00,
	0x8A, 0x00,
	0x8B, 0x00,
	0x8C, 0x00,
	0x8D, 0x00,
	0x8E, 0x00,
	0x8F, 0x00,
	0x90, 0x00,
	0x91, 0x00,
	0x92, 0x00,
	0x93, 0x00,
	0x94, 0x00,
	0x95, 0x00,
	0x96, 0x00,
	0x97, 0x00,
	0x98, 0x00,
	0x99, 0x00,
	0x9A, 0x00,
	0x9B, 0x00,
	0x9C, 0x00,
	0x9D, 0x00,
	0x9E, 0x00,
	0x9F, 0x00,
	0xA0, 0x00,
	0xA1, 0x00,
	0xA2, 0x00,
	0xA3, 0x00,
	0xA4, 0x00,
	0xA5, 0x00,
	0xA6, 0x00,
	0xA7, 0x00,
	0xA8, 0x00,
	0xA9, 0x00,
	0xAA, 0x00,
	0xAB, 0x0C,
	0xAC, 0x00,
	0xAD, 0x00,
	0xAE, 0x14,
	0xAF, 0x0C,
	0xB0, 0x0C,
	0xB1, 0x00,
	0xB2, 0x00,
	0xB3, 0x00,
	0xB4, 0x00,
	0xB5, 0x00,
	0xB6, 0x00,
	0xB7, 0x00,
	0xB8, 0x00,
	0xB9, 0x00,
	0xBA, 0x00,
	0xBB, 0x00,
	0xBC, 0x00,
	0xBD, 0x00,
	0xBE, 0x00,
	0xBF, 0x0C,
	0xC0, 0x00,
	0xC1, 0x00,
	0xC2, 0x00,
	0xC3, 0x00,
	0xC4, 0x00,
	0xC5, 0x00,
	0xC6, 0x00,
	0xC7, 0x00,
	0xC8, 0x00,
	0xC9, 0x00,
	0xCA, 0x00,
	0xCB, 0x00,
	0xCC, 0x00,
	0xCD, 0x00,
	0xCE, 0x00,
	0xCF, 0x00,
	0xD0, 0x00,
	0xD1, 0x00,
	0xD2, 0x00,
	0xD3, 0x00,
	0xD4, 0x00,
	0xD5, 0x00,
	0xD6, 0x00,
	0xD7, 0x00,
	0xD8, 0x00,
	0xD9, 0x00,
	0xDA, 0x00,
	0xDB, 0x00,
	0xDC, 0x00,
	0xDD, 0x00,
	0xDE, 0x00,
	0xDF, 0x00,
	0xE0, 0x00,
	0xE1, 0x00,
	0xE2, 0x00,
	0xE3, 0x00,
	0xE4, 0x00,
	0xE5, 0x00,
	0xE6, 0x00,
	0xE7, 0x00,
	0xE8, 0x00,
	0xE9, 0x00,
	0xEA, 0x00,
	0xEB, 0x00,
	0xEC, 0x00,
	0xED, 0x00,
	0xEE, 0x00,
	0xEF, 0x00,
	0xF0, 0x00,
	0xF1, 0x00,
	0xF2, 0x00,
	0xF3, 0x00,
	0xF4, 0x00,
	0xF5, 0x00,
	0xF6, 0x00,
	0xF7, 0x00,
	0xF8, 0x00,
	0xF9, 0x00,
	0xFA, 0x00,
	0xFB, 0x00,
	0xFC, 0x00,
	0xFD, 0x00,
	0xFE, 0x00,
	0xFF, 0x00 
};

//	New table for blocking specific 0x60 packets in-game.

unsigned char dont_send_60[256*2] = 
{
	0x00, 1,
	0x01, 0,
	0x02, 0,
	0x03, 0,
	0x04, 0,
	0x05, 0,
	0x06, 1,
	0x07, 0,
	0x08, 0,
	0x09, 0,
	0x0A, 0,
	0x0B, 0,
	0x0C, 0,
	0x0D, 0,
	0x0E, 0,
	0x0F, 0,
	0x10, 0,
	0x11, 0,
	0x12, 0,
	0x13, 0,
	0x14, 0,
	0x15, 0,
	0x16, 0,
	0x17, 0,
	0x18, 0,
	0x19, 0,
	0x1A, 0,
	0x1B, 0,
	0x1C, 0,
	0x1D, 0,
	0x1E, 0,
	0x1F, 0,
	0x20, 0,
	0x21, 0,
	0x22, 0,
	0x23, 0,
	0x24, 0,
	0x25, 0,
	0x26, 0,
	0x27, 0,
	0x28, 0,
	0x29, 0,
	0x2A, 0,
	0x2B, 0,
	0x2C, 0,
	0x2D, 0,
	0x2E, 0,
	0x2F, 0,
	0x30, 1,
	0x31, 0,
	0x32, 0,
	0x33, 0,
	0x34, 0,
	0x35, 0,
	0x36, 0,
	0x37, 0,
	0x38, 0,
	0x39, 0,
	0x3A, 0,
	0x3B, 0,
	0x3C, 0,
	0x3D, 0,
	0x3E, 0,
	0x3F, 0,
	0x40, 0,
	0x41, 0,
	0x42, 0,
	0x43, 0,
	0x44, 0,
	0x45, 0,
	0x46, 0,
	0x47, 0,
	0x48, 0,
	0x49, 0,
	0x4A, 0,
	0x4B, 0,
	0x4C, 0,
	0x4D, 0,
	0x4E, 0,
	0x4F, 0,
	0x50, 0,
	0x51, 0,
	0x52, 0,
	0x53, 0,
	0x54, 0,
	0x55, 0,
	0x56, 0,
	0x57, 0,
	0x58, 0,
	0x59, 1,
	0x5A, 1,
	0x5B, 0,
	0x5C, 0,
	0x5D, 1,
	0x5E, 0,
	0x5F, 1,
	0x60, 1,
	0x61, 0,
	0x62, 0,
	0x63, 0,
	0x64, 0,
	0x65, 0,
	0x66, 0,
	0x67, 0,
	0x68, 0,
	0x69, 0,
	0x6A, 0,
	0x6B, 1,
	0x6C, 1,
	0x6D, 1,
	0x6E, 1,
	0x6F, 1,
	0x70, 1,
	0x71, 1,
	0x72, 1,
	0x73, 1,
	0x74, 0,
	0x75, 0,
	0x76, 0,
	0x77, 0,
	0x78, 0,
	0x79, 0,
	0x7A, 0,
	0x7B, 0,
	0x7C, 0,
	0x7D, 0,
	0x7E, 0,
	0x7F, 0,
	0x80, 0,
	0x81, 0,
	0x82, 0,
	0x83, 0,
	0x84, 0,
	0x85, 0,
	0x86, 0,
	0x87, 0,
	0x88, 0,
	0x89, 0,
	0x8A, 0,
	0x8B, 0,
	0x8C, 0,
	0x8D, 0,
	0x8E, 0,
	0x8F, 0,
	0x90, 0,
	0x91, 0,
	0x92, 0,
	0x93, 0,
	0x94, 1,
	0x95, 0,
	0x96, 0,
	0x97, 0,
	0x98, 0,
	0x99, 0,
	0x9A, 0,
	0x9B, 0,
	0x9C, 0,
	0x9D, 0,
	0x9E, 0,
	0x9F, 0,
	0xA0, 0,
	0xA1, 0,
	0xA2, 1,
	0xA3, 0,
	0xA4, 0,
	0xA5, 0,
	0xA6, 1,
	0xA7, 0,
	0xA8, 0,
	0xA9, 0,
	0xAA, 0,
	0xAB, 0,
	0xAC, 0,
	0xAD, 0,
	0xAE, 1,
	0xAF, 0,
	0xB0, 0,
	0xB1, 0,
	0xB2, 0,
	0xB3, 0,
	0xB4, 0,
	0xB5, 1,
	0xB6, 1,
	0xB7, 1,
	0xB8, 1,
	0xB9, 1,
	0xBA, 1,
	0xBB, 1,
	0xBC, 1,
	0xBD, 1,
	0xBE, 1,
	0xBF, 1,
	0xC0, 0,
	0xC1, 1,
	0xC2, 1,
	0xC3, 0,
	0xC4, 0,
	0xC5, 0,
	0xC6, 0,
	0xC7, 0,
	0xC8, 0,
	0xC9, 1,
	0xCA, 1,
	0xCB, 0,
	0xCC, 0,
	0xCD, 1,
	0xCE, 1,
	0xCF, 0,
	0xD0, 0,
	0xD1, 0,
	0xD2, 0,
	0xD3, 0,
	0xD4, 0,
	0xD5, 0,
	0xD6, 0,
	0xD7, 0,
	0xD8, 0,
	0xD9, 0,
	0xDA, 0,
	0xDB, 0,
	0xDC, 0,
	0xDD, 1,
	0xDE, 0,
	0xDF, 1,
	0xE0, 1,
	0xE1, 0,
	0xE2, 0,
	0xE3, 0,
	0xE4, 0,
	0xE5, 0,
	0xE6, 0,
	0xE7, 0,
	0xE8, 0,
	0xE9, 0,
	0xEA, 0,
	0xEB, 0,
	0xEC, 0,
	0xED, 0,
	0xEE, 0,
	0xEF, 0,
	0xF0, 0,
	0xF1, 0,
	0xF2, 0,
	0xF3, 0,
	0xF4, 0,
	0xF5, 0,
	0xF6, 0,
	0xF7, 0,
	0xF8, 0,
	0xF9, 0,
	0xFA, 0,
	0xFB, 0,
	0xFC, 0,
	0xFD, 0,
	0xFE, 0,
	0xFF, 1
};
