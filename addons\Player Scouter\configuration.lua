local function ConfigurationWindow(configuration)
    local this =
    {
        title = "Player Scouter - Configuration",
        open = false,
        changed = false,
    }

    local _configuration = configuration

    local _showWindowSettings = function()
        local success

        if imgui.TreeNodeEx("General", "DefaultOpen") then
            if imgui.Checkbox("Enable", _configuration.enable) then
                _configuration.enable = not _configuration.enable
                this.changed = true
            end

            if imgui.Checkbox("Hide when menu is open", _configuration.hideWhenMenuOpen) then
                _configuration.hideWhenMenuOpen = not _configuration.hideWhenMenuOpen
                this.changed = true
            end

            if imgui.Checkbox("Hide when menu is unavailable", _configuration.hideWhenMenuUnavailable) then
                _configuration.hideWhenMenuUnavailable = not _configuration.hideWhenMenuUnavailable
                this.changed = true
            end

            if imgui.Checkbox("Hide when symbol chat is open", _configuration.hideWhenSymbolChatOpen) then
                _configuration.hideWhenSymbolChatOpen = not _configuration.hideWhenSymbolChatOpen
                this.changed = true
            end

            imgui.TreePop()
        end

        if imgui.TreeNodeEx("Display Options") then
            if imgui.Checkbox("Show Name", _configuration.showName) then
                _configuration.showName = not _configuration.showName
                this.changed = true
            end

            if imgui.Checkbox("Show HP Bar", _configuration.showHpBar) then
                _configuration.showHpBar = not _configuration.showHpBar
                this.changed = true
            end

            if imgui.Checkbox("Show HP Text", _configuration.showHpText) then
                _configuration.showHpText = not _configuration.showHpText
                this.changed = true
            end

            if imgui.Checkbox("Show Buffs", _configuration.showBuffs) then
                _configuration.showBuffs = not _configuration.showBuffs
                this.changed = true
            end

            if imgui.Checkbox("Transparent Window", _configuration.transparentWindow) then
                _configuration.transparentWindow = not _configuration.transparentWindow
                this.changed = true
            end

            if imgui.Checkbox("Clamp to View", _configuration.clampToView) then
                _configuration.clampToView = not _configuration.clampToView
                this.changed = true
            end

            imgui.PushItemWidth(150)
            success, _configuration.trackerYOffset = imgui.InputInt("Vertical Offset", _configuration.trackerYOffset, 1, 5)
            if success then
                this.changed = true
            end

            success, _configuration.minimumWidth = imgui.InputInt("Minimum Width", _configuration.minimumWidth, 1, 5)
            if success then
                this.changed = true
            end

            success, _configuration.clampBorderX = imgui.InputInt("Clamp Border X", _configuration.clampBorderX, 0, 100)
            if success then
                this.changed = true
            end

            success, _configuration.clampBorderY = imgui.InputInt("Clamp Border Y", _configuration.clampBorderY, 0, 100)
            if success then
                this.changed = true
            end
            imgui.PopItemWidth()

            imgui.TreePop()
        end
    end

    this.Update = function()
        if this.open == false then
            return
        end

        local success

        imgui.SetNextWindowSize(300, 320, 'FirstUseEver')
        success, this.open = imgui.Begin(this.title, this.open)

        _showWindowSettings()

        imgui.End()
    end

    return this
end

return
{
    ConfigurationWindow = ConfigurationWindow,
}
