local function ConfigurationWindow(configuration)
    local this =
    {
        title = "Consumable Reader - Configuration",
        open = false,
        changed = false,
    }

    local _configuration = configuration

    local _showWindowSettings = function()
        local success
        local anchorList =
        {
            "Top Left (Disabled)", "Left", "Bottom Left",
            "Top", "Center", "Bottom",
            "Top Right", "Right", "Bottom Right",
        }

        if imgui.Checkbox("Enable", _configuration.EnableWindow) then
            _configuration.EnableWindow = not _configuration.EnableWindow
            _configuration.changed = true
            this.changed = true
        end
        imgui.Text("Tools")

        if imgui.Checkbox("Monomate", _configuration.Monomate) then
            _configuration.Monomate = not _configuration.Monomate
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.MonomateName = imgui.InputText("Monomate Name", _configuration.MonomateName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Monomate Reset')) then
            _configuration.MonomateName = " Monomate "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Dimate", _configuration.Dimate) then
            _configuration.Dimate = not _configuration.Dimate
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.DimateName = imgui.InputText("Dimate Name", _configuration.DimateName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Dimate Reset')) then
            _configuration.DimateName = " Dimate "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Trimate", _configuration.Trimate) then
            _configuration.Trimate = not _configuration.Trimate
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.TrimateName = imgui.InputText("Trimate Name", _configuration.TrimateName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Trimate Reset')) then
            _configuration.TrimateName = " Trimate "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Monofluid", _configuration.Monofluid) then
            _configuration.Monofluid = not _configuration.Monofluid
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.MonofluidName = imgui.InputText("Monofluid Name", _configuration.MonofluidName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Monofluid Reset')) then
            _configuration.MonofluidName = " Monofluid "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Difluid", _configuration.Difluid) then
            _configuration.Difluid = not _configuration.Difluid
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.DifluidName = imgui.InputText("Difluid Name", _configuration.DifluidName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Difluid Reset')) then
            _configuration.DifluidName = " Difluid "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Trifluid", _configuration.Trifluid) then
            _configuration.Trifluid = not _configuration.Trifluid
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.TrifluidName = imgui.InputText("Trifluid Name", _configuration.TrifluidName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Trifluid Reset')) then
            _configuration.TrifluidName = " Trifluid "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Sol", _configuration.Sol) then
            _configuration.Sol = not _configuration.Sol
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.SolName = imgui.InputText("Sol Atomizer Name", _configuration.SolName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Sol Atomizer Reset')) then
            _configuration.SolName = " Sol Atomizer "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Moon", _configuration.Moon) then
            _configuration.Moon = not _configuration.Moon
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.MoonName = imgui.InputText("Moon Atomizer Name", _configuration.MoonName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Moon Atomizer Reset')) then
            _configuration.MoonName = " Moon Atomizer "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Star", _configuration.Star) then
            _configuration.Star = not _configuration.Star
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.StarName = imgui.InputText("Star Atomizer Name", _configuration.StarName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Star Atomizer Reset')) then
            _configuration.StarName = " Star Atomizer "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Antidote", _configuration.Antidote) then
            _configuration.Antidote = not _configuration.Antidote
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.AntidoteName = imgui.InputText("Antidote Name", _configuration.AntidoteName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Antidote Reset')) then
            _configuration.AntidoteName = " Antidote "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Antiparalysis", _configuration.Antiparalysis) then
            _configuration.Antiparalysis = not _configuration.Antiparalysis
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.AntiparalysisName = imgui.InputText("Antiparalysis Name", _configuration.AntiparalysisName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Antiparalysis Reset')) then
            _configuration.AntiparalysisName = " Antiparalysis "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Telepipe", _configuration.Telepipe) then
            _configuration.Telepipe = not _configuration.Telepipe
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.TelepipeName = imgui.InputText("Telepipe Name", _configuration.TelepipeName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Telepipe Reset')) then
            _configuration.TelepipeName = " Telepipe "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Trap", _configuration.Trap) then
            _configuration.Trap = not _configuration.Trap
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.TrapName = imgui.InputText("Trap Vision Name", _configuration.TrapName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Trap Vision Reset')) then
            _configuration.TrapName = " Trap Vision "
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Scape Doll", _configuration.Scape) then
            _configuration.Scape = not _configuration.Scape
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.ScapeName = imgui.InputText("Scape Doll Name", _configuration.ScapeName, 20)
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        if (imgui.Button('Scape Doll Reset')) then
            _configuration.ScapeName = " Scape Doll "
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.ScapeCountLow = imgui.InputInt("Low", _configuration.ScapeCountLow)
        imgui.PopItemWidth()
        if success then
            _configuration.changed = true
            this.changed = true
        end
        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.ScapeCountHigh = imgui.InputInt("High", _configuration.ScapeCountHigh)
        imgui.PopItemWidth()
        if success then
            _configuration.changed = true
            this.changed = true
        end

        imgui.Text("Display")
        if imgui.Checkbox("No title bar", _configuration.NoTitleBar == "NoTitleBar") then
            if _configuration.NoTitleBar == "NoTitleBar" then
                _configuration.NoTitleBar = ""
            else
                _configuration.NoTitleBar = "NoTitleBar"
            end
            _configuration.changed = true
            this.changed = true
        end
        if imgui.Checkbox("No resize", _configuration.NoResize == "NoResize") then
            if _configuration.NoResize == "NoResize" then
                _configuration.NoResize = ""
            else
                _configuration.NoResize = "NoResize"
            end
            _configuration.changed = true
            this.changed = true
        end
        if imgui.Checkbox("No move", _configuration.NoMove == "NoMove") then
            if _configuration.NoMove == "NoMove" then
                _configuration.NoMove = ""
            else
                _configuration.NoMove = "NoMove"
            end
            _configuration.changed = true
            this.changed = true
        end
        if imgui.Checkbox("Always Auto Resize", _configuration.AlwaysAutoResize == "AlwaysAutoResize") then
            if _configuration.AlwaysAutoResize == "AlwaysAutoResize" then
                _configuration.AlwaysAutoResize = ""
            else
                _configuration.AlwaysAutoResize = "AlwaysAutoResize"
            end
            _configuration.changed = true
            this.changed = true
        end

        if imgui.Checkbox("Transparent window", _configuration.TransparentWindow) then
            _configuration.TransparentWindow = not _configuration.TransparentWindow
            _configuration.changed = true
            this.changed = true
        end
        if imgui.Checkbox("Hide when menus are open", _configuration.HideWhenMenu) then
            _configuration.HideWhenMenu = not _configuration.HideWhenMenu
            this.changed = true
        end
        if imgui.Checkbox("Hide when symbol chat/word select is open", _configuration.HideWhenSymbolChat) then
            _configuration.HideWhenSymbolChat = not _configuration.HideWhenSymbolChat
            this.changed = true
        end
        if imgui.Checkbox("Hide when the menu is unavailable", _configuration.HideWhenMenuUnavailable) then
            _configuration.HideWhenMenuUnavailable = not _configuration.HideWhenMenuUnavailable
            this.changed = true
        end
        imgui.Text("Position and Size")
        imgui.PushItemWidth(200)
        success, _configuration.Anchor = imgui.Combo("Anchor", _configuration.Anchor, anchorList, table.getn(anchorList))
        imgui.PopItemWidth()
        if success then
            _configuration.changed = true
            this.changed = true
        end

        imgui.PushItemWidth(100)
        success, _configuration.X = imgui.InputInt("X", _configuration.X)
        imgui.PopItemWidth()
        if success then
            _configuration.changed = true
            this.changed = true
        end

        imgui.SameLine(0, 38)
        imgui.PushItemWidth(100)
        success, _configuration.Y = imgui.InputInt("Y", _configuration.Y)
        imgui.PopItemWidth()
        if success then
            _configuration.changed = true
            this.changed = true
        end

        imgui.PushItemWidth(100)
        success, _configuration.W = imgui.InputInt("Width", _configuration.W)
        imgui.PopItemWidth()
        if success then
            _configuration.changed = true
            this.changed = true
        end

        imgui.SameLine(0, 10)
        imgui.PushItemWidth(100)
        success, _configuration.H = imgui.InputInt("Height", _configuration.H)
        imgui.PopItemWidth()
        if success then
            _configuration.changed = true
            this.changed = true
        end
    end

    this.Update = function()
        if this.open == false then
            return
        end

        local success

        imgui.SetNextWindowSize(500, 400, 'FirstUseEver')
        success, this.open = imgui.Begin(this.title, this.open)

        _showWindowSettings()

        imgui.End()
    end

    return this
end

return
{
    ConfigurationWindow = ConfigurationWindow,
}
