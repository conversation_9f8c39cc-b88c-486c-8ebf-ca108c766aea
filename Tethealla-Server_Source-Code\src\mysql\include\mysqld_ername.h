/* Autogenerated file, please don't edit */

{ "ER_HASHCHK", 1000, "hashchk" },
{ "ER_NISAMCHK", 1001, "isamchk" },
{ "ER_NO", 1002, "NO" },
{ "ER_YES", 1003, "YES" },
{ "ER_CANT_CREATE_FILE", 1004, "Can\'t create file \'%-.200s\' (errno: %d - %s)" },
{ "ER_CANT_CREATE_TABLE", 1005, "Can\'t create table \'%-.200s\' (errno: %d)" },
{ "ER_CANT_CREATE_DB", 1006, "Can\'t create database \'%-.192s\' (errno: %d)" },
{ "ER_DB_CREATE_EXISTS", 1007, "Can\'t create database \'%-.192s\'; database exists" },
{ "ER_DB_DROP_EXISTS", 1008, "Can\'t drop database \'%-.192s\'; database doesn\'t exist" },
{ "ER_DB_DROP_DELETE", 1009, "Error dropping database (can\'t delete \'%-.192s\', errno: %d)" },
{ "ER_DB_DROP_RMDIR", 1010, "Error dropping database (can\'t rmdir \'%-.192s\', errno: %d)" },
{ "ER_CANT_DELETE_FILE", 1011, "Error on delete of \'%-.192s\' (errno: %d - %s)" },
{ "ER_CANT_FIND_SYSTEM_REC", 1012, "Can\'t read record in system table" },
{ "ER_CANT_GET_STAT", 1013, "Can\'t get status of \'%-.200s\' (errno: %d - %s)" },
{ "ER_CANT_GET_WD", 1014, "Can\'t get working directory (errno: %d - %s)" },
{ "ER_CANT_LOCK", 1015, "Can\'t lock file (errno: %d - %s)" },
{ "ER_CANT_OPEN_FILE", 1016, "Can\'t open file: \'%-.200s\' (errno: %d - %s)" },
{ "ER_FILE_NOT_FOUND", 1017, "Can\'t find file: \'%-.200s\' (errno: %d - %s)" },
{ "ER_CANT_READ_DIR", 1018, "Can\'t read dir of \'%-.192s\' (errno: %d - %s)" },
{ "ER_CANT_SET_WD", 1019, "Can\'t change dir to \'%-.192s\' (errno: %d - %s)" },
{ "ER_CHECKREAD", 1020, "Record has changed since last read in table \'%-.192s\'" },
{ "ER_DISK_FULL", 1021, "Disk full (%s); waiting for someone to free some space... (errno: %d - %s)" },
{ "ER_DUP_KEY", 1022, "Can\'t write; duplicate key in table \'%-.192s\'" },
{ "ER_ERROR_ON_CLOSE", 1023, "Error on close of \'%-.192s\' (errno: %d - %s)" },
{ "ER_ERROR_ON_READ", 1024, "Error reading file \'%-.200s\' (errno: %d - %s)" },
{ "ER_ERROR_ON_RENAME", 1025, "Error on rename of \'%-.210s\' to \'%-.210s\' (errno: %d - %s)" },
{ "ER_ERROR_ON_WRITE", 1026, "Error writing file \'%-.200s\' (errno: %d - %s)" },
{ "ER_FILE_USED", 1027, "\'%-.192s\' is locked against change" },
{ "ER_FILSORT_ABORT", 1028, "Sort aborted" },
{ "ER_FORM_NOT_FOUND", 1029, "View \'%-.192s\' doesn\'t exist for \'%-.192s\'" },
{ "ER_GET_ERRNO", 1030, "Got error %d from storage engine" },
{ "ER_ILLEGAL_HA", 1031, "Table storage engine for \'%-.192s\' doesn\'t have this option" },
{ "ER_KEY_NOT_FOUND", 1032, "Can\'t find record in \'%-.192s\'" },
{ "ER_NOT_FORM_FILE", 1033, "Incorrect information in file: \'%-.200s\'" },
{ "ER_NOT_KEYFILE", 1034, "Incorrect key file for table \'%-.200s\'; try to repair it" },
{ "ER_OLD_KEYFILE", 1035, "Old key file for table \'%-.192s\'; repair it!" },
{ "ER_OPEN_AS_READONLY", 1036, "Table \'%-.192s\' is read only" },
{ "ER_OUTOFMEMORY", 1037, "Out of memory; restart server and try again (needed %d bytes)" },
{ "ER_OUT_OF_SORTMEMORY", 1038, "Out of sort memory, consider increasing server sort buffer size" },
{ "ER_UNEXPECTED_EOF", 1039, "Unexpected EOF found when reading file \'%-.192s\' (errno: %d - %s)" },
{ "ER_CON_COUNT_ERROR", 1040, "Too many connections" },
{ "ER_OUT_OF_RESOURCES", 1041, "Out of memory; check if mysqld or some other process uses all available memory; if not, you may have to use \'ulimit\' to allow mysqld to use more memory or you can add more swap space" },
{ "ER_BAD_HOST_ERROR", 1042, "Can\'t get hostname for your address" },
{ "ER_HANDSHAKE_ERROR", 1043, "Bad handshake" },
{ "ER_DBACCESS_DENIED_ERROR", 1044, "Access denied for user \'%-.48s\'@\'%-.64s\' to database \'%-.192s\'" },
{ "ER_ACCESS_DENIED_ERROR", 1045, "Access denied for user \'%-.48s\'@\'%-.64s\' (using password: %s)" },
{ "ER_NO_DB_ERROR", 1046, "No database selected" },
{ "ER_UNKNOWN_COM_ERROR", 1047, "Unknown command" },
{ "ER_BAD_NULL_ERROR", 1048, "Column \'%-.192s\' cannot be null" },
{ "ER_BAD_DB_ERROR", 1049, "Unknown database \'%-.192s\'" },
{ "ER_TABLE_EXISTS_ERROR", 1050, "Table \'%-.192s\' already exists" },
{ "ER_BAD_TABLE_ERROR", 1051, "Unknown table \'%-.100s\'" },
{ "ER_NON_UNIQ_ERROR", 1052, "Column \'%-.192s\' in %-.192s is ambiguous" },
{ "ER_SERVER_SHUTDOWN", 1053, "Server shutdown in progress" },
{ "ER_BAD_FIELD_ERROR", 1054, "Unknown column \'%-.192s\' in \'%-.192s\'" },
{ "ER_WRONG_FIELD_WITH_GROUP", 1055, "\'%-.192s\' isn\'t in GROUP BY" },
{ "ER_WRONG_GROUP_FIELD", 1056, "Can\'t group on \'%-.192s\'" },
{ "ER_WRONG_SUM_SELECT", 1057, "Statement has sum functions and columns in same statement" },
{ "ER_WRONG_VALUE_COUNT", 1058, "Column count doesn\'t match value count" },
{ "ER_TOO_LONG_IDENT", 1059, "Identifier name \'%-.100s\' is too long" },
{ "ER_DUP_FIELDNAME", 1060, "Duplicate column name \'%-.192s\'" },
{ "ER_DUP_KEYNAME", 1061, "Duplicate key name \'%-.192s\'" },
{ "ER_DUP_ENTRY", 1062, "Duplicate entry \'%-.192s\' for key %d" },
{ "ER_WRONG_FIELD_SPEC", 1063, "Incorrect column specifier for column \'%-.192s\'" },
{ "ER_PARSE_ERROR", 1064, "%s near \'%-.80s\' at line %d" },
{ "ER_EMPTY_QUERY", 1065, "Query was empty" },
{ "ER_NONUNIQ_TABLE", 1066, "Not unique table/alias: \'%-.192s\'" },
{ "ER_INVALID_DEFAULT", 1067, "Invalid default value for \'%-.192s\'" },
{ "ER_MULTIPLE_PRI_KEY", 1068, "Multiple primary key defined" },
{ "ER_TOO_MANY_KEYS", 1069, "Too many keys specified; max %d keys allowed" },
{ "ER_TOO_MANY_KEY_PARTS", 1070, "Too many key parts specified; max %d parts allowed" },
{ "ER_TOO_LONG_KEY", 1071, "Specified key was too long; max key length is %d bytes" },
{ "ER_KEY_COLUMN_DOES_NOT_EXITS", 1072, "Key column \'%-.192s\' doesn\'t exist in table" },
{ "ER_BLOB_USED_AS_KEY", 1073, "BLOB column \'%-.192s\' can\'t be used in key specification with the used table type" },
{ "ER_TOO_BIG_FIELDLENGTH", 1074, "Column length too big for column \'%-.192s\' (max = %lu); use BLOB or TEXT instead" },
{ "ER_WRONG_AUTO_KEY", 1075, "Incorrect table definition; there can be only one auto column and it must be defined as a key" },
{ "ER_READY", 1076, "%s: ready for connections.\nVersion: \'%s\'  socket: \'%s\'  port: %d" },
{ "ER_NORMAL_SHUTDOWN", 1077, "%s: Normal shutdown\n" },
{ "ER_GOT_SIGNAL", 1078, "%s: Got signal %d. Aborting!\n" },
{ "ER_SHUTDOWN_COMPLETE", 1079, "%s: Shutdown complete\n" },
{ "ER_FORCING_CLOSE", 1080, "%s: Forcing close of thread %ld  user: \'%-.48s\'\n" },
{ "ER_IPSOCK_ERROR", 1081, "Can\'t create IP socket" },
{ "ER_NO_SUCH_INDEX", 1082, "Table \'%-.192s\' has no index like the one used in CREATE INDEX; recreate the table" },
{ "ER_WRONG_FIELD_TERMINATORS", 1083, "Field separator argument is not what is expected; check the manual" },
{ "ER_BLOBS_AND_NO_TERMINATED", 1084, "You can\'t use fixed rowlength with BLOBs; please use \'fields terminated by\'" },
{ "ER_TEXTFILE_NOT_READABLE", 1085, "The file \'%-.128s\' must be in the database directory or be readable by all" },
{ "ER_FILE_EXISTS_ERROR", 1086, "File \'%-.200s\' already exists" },
{ "ER_LOAD_INFO", 1087, "Records: %ld  Deleted: %ld  Skipped: %ld  Warnings: %ld" },
{ "ER_ALTER_INFO", 1088, "Records: %ld  Duplicates: %ld" },
{ "ER_WRONG_SUB_KEY", 1089, "Incorrect prefix key; the used key part isn\'t a string, the used length is longer than the key part, or the storage engine doesn\'t support unique prefix keys" },
{ "ER_CANT_REMOVE_ALL_FIELDS", 1090, "You can\'t delete all columns with ALTER TABLE; use DROP TABLE instead" },
{ "ER_CANT_DROP_FIELD_OR_KEY", 1091, "Can\'t DROP \'%-.192s\'; check that column/key exists" },
{ "ER_INSERT_INFO", 1092, "Records: %ld  Duplicates: %ld  Warnings: %ld" },
{ "ER_UPDATE_TABLE_USED", 1093, "You can\'t specify target table \'%-.192s\' for update in FROM clause" },
{ "ER_NO_SUCH_THREAD", 1094, "Unknown thread id: %lu" },
{ "ER_KILL_DENIED_ERROR", 1095, "You are not owner of thread %lu" },
{ "ER_NO_TABLES_USED", 1096, "No tables used" },
{ "ER_TOO_BIG_SET", 1097, "Too many strings for column %-.192s and SET" },
{ "ER_NO_UNIQUE_LOGFILE", 1098, "Can\'t generate a unique log-filename %-.200s.(1-999)\n" },
{ "ER_TABLE_NOT_LOCKED_FOR_WRITE", 1099, "Table \'%-.192s\' was locked with a READ lock and can\'t be updated" },
{ "ER_TABLE_NOT_LOCKED", 1100, "Table \'%-.192s\' was not locked with LOCK TABLES" },
{ "ER_BLOB_CANT_HAVE_DEFAULT", 1101, "BLOB, TEXT, GEOMETRY or JSON column \'%-.192s\' can\'t have a default value" },
{ "ER_WRONG_DB_NAME", 1102, "Incorrect database name \'%-.100s\'" },
{ "ER_WRONG_TABLE_NAME", 1103, "Incorrect table name \'%-.100s\'" },
{ "ER_TOO_BIG_SELECT", 1104, "The SELECT would examine more than MAX_JOIN_SIZE rows; check your WHERE and use SET SQL_BIG_SELECTS=1 or SET MAX_JOIN_SIZE=# if the SELECT is okay" },
{ "ER_UNKNOWN_ERROR", 1105, "Unknown error" },
{ "ER_UNKNOWN_PROCEDURE", 1106, "Unknown procedure \'%-.192s\'" },
{ "ER_WRONG_PARAMCOUNT_TO_PROCEDURE", 1107, "Incorrect parameter count to procedure \'%-.192s\'" },
{ "ER_WRONG_PARAMETERS_TO_PROCEDURE", 1108, "Incorrect parameters to procedure \'%-.192s\'" },
{ "ER_UNKNOWN_TABLE", 1109, "Unknown table \'%-.192s\' in %-.32s" },
{ "ER_FIELD_SPECIFIED_TWICE", 1110, "Column \'%-.192s\' specified twice" },
{ "ER_INVALID_GROUP_FUNC_USE", 1111, "Invalid use of group function" },
{ "ER_UNSUPPORTED_EXTENSION", 1112, "Table \'%-.192s\' uses an extension that doesn\'t exist in this MySQL version" },
{ "ER_TABLE_MUST_HAVE_COLUMNS", 1113, "A table must have at least 1 column" },
{ "ER_RECORD_FILE_FULL", 1114, "The table \'%-.192s\' is full" },
{ "ER_UNKNOWN_CHARACTER_SET", 1115, "Unknown character set: \'%-.64s\'" },
{ "ER_TOO_MANY_TABLES", 1116, "Too many tables; MySQL can only use %d tables in a join" },
{ "ER_TOO_MANY_FIELDS", 1117, "Too many columns" },
{ "ER_TOO_BIG_ROWSIZE", 1118, "Row size too large. The maximum row size for the used table type, not counting BLOBs, is %ld. This includes storage overhead, check the manual. You have to change some columns to TEXT or BLOBs" },
{ "ER_STACK_OVERRUN", 1119, "Thread stack overrun:  Used: %ld of a %ld stack.  Use \'mysqld --thread_stack=#\' to specify a bigger stack if needed" },
{ "ER_WRONG_OUTER_JOIN", 1120, "Cross dependency found in OUTER JOIN; examine your ON conditions" },
{ "ER_NULL_COLUMN_IN_INDEX", 1121, "Table handler doesn\'t support NULL in given index. Please change column \'%-.192s\' to be NOT NULL or use another handler" },
{ "ER_CANT_FIND_UDF", 1122, "Can\'t load function \'%-.192s\'" },
{ "ER_CANT_INITIALIZE_UDF", 1123, "Can\'t initialize function \'%-.192s\'; %-.80s" },
{ "ER_UDF_NO_PATHS", 1124, "No paths allowed for shared library" },
{ "ER_UDF_EXISTS", 1125, "Function \'%-.192s\' already exists" },
{ "ER_CANT_OPEN_LIBRARY", 1126, "Can\'t open shared library \'%-.192s\' (errno: %d %-.128s)" },
{ "ER_CANT_FIND_DL_ENTRY", 1127, "Can\'t find symbol \'%-.128s\' in library" },
{ "ER_FUNCTION_NOT_DEFINED", 1128, "Function \'%-.192s\' is not defined" },
{ "ER_HOST_IS_BLOCKED", 1129, "Host \'%-.64s\' is blocked because of many connection errors; unblock with \'mysqladmin flush-hosts\'" },
{ "ER_HOST_NOT_PRIVILEGED", 1130, "Host \'%-.64s\' is not allowed to connect to this MySQL server" },
{ "ER_PASSWORD_ANONYMOUS_USER", 1131, "You are using MySQL as an anonymous user and anonymous users are not allowed to change passwords" },
{ "ER_PASSWORD_NOT_ALLOWED", 1132, "You must have privileges to update tables in the mysql database to be able to change passwords for others" },
{ "ER_PASSWORD_NO_MATCH", 1133, "Can\'t find any matching row in the user table" },
{ "ER_UPDATE_INFO", 1134, "Rows matched: %ld  Changed: %ld  Warnings: %ld" },
{ "ER_CANT_CREATE_THREAD", 1135, "Can\'t create a new thread (errno %d); if you are not out of available memory, you can consult the manual for a possible OS-dependent bug" },
{ "ER_WRONG_VALUE_COUNT_ON_ROW", 1136, "Column count doesn\'t match value count at row %ld" },
{ "ER_CANT_REOPEN_TABLE", 1137, "Can\'t reopen table: \'%-.192s\'" },
{ "ER_INVALID_USE_OF_NULL", 1138, "Invalid use of NULL value" },
{ "ER_REGEXP_ERROR", 1139, "Got error \'%-.64s\' from regexp" },
{ "ER_MIX_OF_GROUP_FUNC_AND_FIELDS", 1140, "Mixing of GROUP columns (MIN(),MAX(),COUNT(),...) with no GROUP columns is illegal if there is no GROUP BY clause" },
{ "ER_NONEXISTING_GRANT", 1141, "There is no such grant defined for user \'%-.48s\' on host \'%-.64s\'" },
{ "ER_TABLEACCESS_DENIED_ERROR", 1142, "%-.128s command denied to user \'%-.48s\'@\'%-.64s\' for table \'%-.64s\'" },
{ "ER_COLUMNACCESS_DENIED_ERROR", 1143, "%-.16s command denied to user \'%-.48s\'@\'%-.64s\' for column \'%-.192s\' in table \'%-.192s\'" },
{ "ER_ILLEGAL_GRANT_FOR_TABLE", 1144, "Illegal GRANT/REVOKE command; please consult the manual to see which privileges can be used" },
{ "ER_GRANT_WRONG_HOST_OR_USER", 1145, "The host or user argument to GRANT is too long" },
{ "ER_NO_SUCH_TABLE", 1146, "Table \'%-.192s.%-.192s\' doesn\'t exist" },
{ "ER_NONEXISTING_TABLE_GRANT", 1147, "There is no such grant defined for user \'%-.48s\' on host \'%-.64s\' on table \'%-.192s\'" },
{ "ER_NOT_ALLOWED_COMMAND", 1148, "The used command is not allowed with this MySQL version" },
{ "ER_SYNTAX_ERROR", 1149, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use" },
{ "ER_UNUSED1", 1150, "Delayed insert thread couldn\'t get requested lock for table %-.192s" },
{ "ER_UNUSED2", 1151, "Too many delayed threads in use" },
{ "ER_ABORTING_CONNECTION", 1152, "Aborted connection %ld to db: \'%-.192s\' user: \'%-.48s\' (%-.64s)" },
{ "ER_NET_PACKET_TOO_LARGE", 1153, "Got a packet bigger than \'max_allowed_packet\' bytes" },
{ "ER_NET_READ_ERROR_FROM_PIPE", 1154, "Got a read error from the connection pipe" },
{ "ER_NET_FCNTL_ERROR", 1155, "Got an error from fcntl()" },
{ "ER_NET_PACKETS_OUT_OF_ORDER", 1156, "Got packets out of order" },
{ "ER_NET_UNCOMPRESS_ERROR", 1157, "Couldn\'t uncompress communication packet" },
{ "ER_NET_READ_ERROR", 1158, "Got an error reading communication packets" },
{ "ER_NET_READ_INTERRUPTED", 1159, "Got timeout reading communication packets" },
{ "ER_NET_ERROR_ON_WRITE", 1160, "Got an error writing communication packets" },
{ "ER_NET_WRITE_INTERRUPTED", 1161, "Got timeout writing communication packets" },
{ "ER_TOO_LONG_STRING", 1162, "Result string is longer than \'max_allowed_packet\' bytes" },
{ "ER_TABLE_CANT_HANDLE_BLOB", 1163, "The used table type doesn\'t support BLOB/TEXT columns" },
{ "ER_TABLE_CANT_HANDLE_AUTO_INCREMENT", 1164, "The used table type doesn\'t support AUTO_INCREMENT columns" },
{ "ER_UNUSED3", 1165, "INSERT DELAYED can\'t be used with table \'%-.192s\' because it is locked with LOCK TABLES" },
{ "ER_WRONG_COLUMN_NAME", 1166, "Incorrect column name \'%-.100s\'" },
{ "ER_WRONG_KEY_COLUMN", 1167, "The used storage engine can\'t index column \'%-.192s\'" },
{ "ER_WRONG_MRG_TABLE", 1168, "Unable to open underlying table which is differently defined or of non-MyISAM type or doesn\'t exist" },
{ "ER_DUP_UNIQUE", 1169, "Can\'t write, because of unique constraint, to table \'%-.192s\'" },
{ "ER_BLOB_KEY_WITHOUT_LENGTH", 1170, "BLOB/TEXT column \'%-.192s\' used in key specification without a key length" },
{ "ER_PRIMARY_CANT_HAVE_NULL", 1171, "All parts of a PRIMARY KEY must be NOT NULL; if you need NULL in a key, use UNIQUE instead" },
{ "ER_TOO_MANY_ROWS", 1172, "Result consisted of more than one row" },
{ "ER_REQUIRES_PRIMARY_KEY", 1173, "This table type requires a primary key" },
{ "ER_NO_RAID_COMPILED", 1174, "This version of MySQL is not compiled with RAID support" },
{ "ER_UPDATE_WITHOUT_KEY_IN_SAFE_MODE", 1175, "You are using safe update mode and you tried to update a table without a WHERE that uses a KEY column" },
{ "ER_KEY_DOES_NOT_EXITS", 1176, "Key \'%-.192s\' doesn\'t exist in table \'%-.192s\'" },
{ "ER_CHECK_NO_SUCH_TABLE", 1177, "Can\'t open table" },
{ "ER_CHECK_NOT_IMPLEMENTED", 1178, "The storage engine for the table doesn\'t support %s" },
{ "ER_CANT_DO_THIS_DURING_AN_TRANSACTION", 1179, "You are not allowed to execute this command in a transaction" },
{ "ER_ERROR_DURING_COMMIT", 1180, "Got error %d during COMMIT" },
{ "ER_ERROR_DURING_ROLLBACK", 1181, "Got error %d during ROLLBACK" },
{ "ER_ERROR_DURING_FLUSH_LOGS", 1182, "Got error %d during FLUSH_LOGS" },
{ "ER_ERROR_DURING_CHECKPOINT", 1183, "Got error %d during CHECKPOINT" },
{ "ER_NEW_ABORTING_CONNECTION", 1184, "Aborted connection %u to db: \'%-.192s\' user: \'%-.48s\' host: \'%-.64s\' (%-.64s)" },
{ "ER_DUMP_NOT_IMPLEMENTED", 1185, "The storage engine for the table does not support binary table dump" },
{ "ER_FLUSH_MASTER_BINLOG_CLOSED", 1186, "Binlog closed, cannot RESET MASTER" },
{ "ER_INDEX_REBUILD", 1187, "Failed rebuilding the index of  dumped table \'%-.192s\'" },
{ "ER_MASTER", 1188, "Error from master: \'%-.64s\'" },
{ "ER_MASTER_NET_READ", 1189, "Net error reading from master" },
{ "ER_MASTER_NET_WRITE", 1190, "Net error writing to master" },
{ "ER_FT_MATCHING_KEY_NOT_FOUND", 1191, "Can\'t find FULLTEXT index matching the column list" },
{ "ER_LOCK_OR_ACTIVE_TRANSACTION", 1192, "Can\'t execute the given command because you have active locked tables or an active transaction" },
{ "ER_UNKNOWN_SYSTEM_VARIABLE", 1193, "Unknown system variable \'%-.64s\'" },
{ "ER_CRASHED_ON_USAGE", 1194, "Table \'%-.192s\' is marked as crashed and should be repaired" },
{ "ER_CRASHED_ON_REPAIR", 1195, "Table \'%-.192s\' is marked as crashed and last (automatic?) repair failed" },
{ "ER_WARNING_NOT_COMPLETE_ROLLBACK", 1196, "Some non-transactional changed tables couldn\'t be rolled back" },
{ "ER_TRANS_CACHE_FULL", 1197, "Multi-statement transaction required more than \'max_binlog_cache_size\' bytes of storage; increase this mysqld variable and try again" },
{ "ER_SLAVE_MUST_STOP", 1198, "This operation cannot be performed with a running slave; run STOP SLAVE first" },
{ "ER_SLAVE_NOT_RUNNING", 1199, "This operation requires a running slave; configure slave and do START SLAVE" },
{ "ER_BAD_SLAVE", 1200, "The server is not configured as slave; fix in config file or with CHANGE MASTER TO" },
{ "ER_MASTER_INFO", 1201, "Could not initialize master info structure; more error messages can be found in the MySQL error log" },
{ "ER_SLAVE_THREAD", 1202, "Could not create slave thread; check system resources" },
{ "ER_TOO_MANY_USER_CONNECTIONS", 1203, "User %-.64s already has more than \'max_user_connections\' active connections" },
{ "ER_SET_CONSTANTS_ONLY", 1204, "You may only use constant expressions with SET" },
{ "ER_LOCK_WAIT_TIMEOUT", 1205, "Lock wait timeout exceeded; try restarting transaction" },
{ "ER_LOCK_TABLE_FULL", 1206, "The total number of locks exceeds the lock table size" },
{ "ER_READ_ONLY_TRANSACTION", 1207, "Update locks cannot be acquired during a READ UNCOMMITTED transaction" },
{ "ER_DROP_DB_WITH_READ_LOCK", 1208, "DROP DATABASE not allowed while thread is holding global read lock" },
{ "ER_CREATE_DB_WITH_READ_LOCK", 1209, "CREATE DATABASE not allowed while thread is holding global read lock" },
{ "ER_WRONG_ARGUMENTS", 1210, "Incorrect arguments to %s" },
{ "ER_NO_PERMISSION_TO_CREATE_USER", 1211, "\'%-.48s\'@\'%-.64s\' is not allowed to create new users" },
{ "ER_UNION_TABLES_IN_DIFFERENT_DIR", 1212, "Incorrect table definition; all MERGE tables must be in the same database" },
{ "ER_LOCK_DEADLOCK", 1213, "Deadlock found when trying to get lock; try restarting transaction" },
{ "ER_TABLE_CANT_HANDLE_FT", 1214, "The used table type doesn\'t support FULLTEXT indexes" },
{ "ER_CANNOT_ADD_FOREIGN", 1215, "Cannot add foreign key constraint" },
{ "ER_NO_REFERENCED_ROW", 1216, "Cannot add or update a child row: a foreign key constraint fails" },
{ "ER_ROW_IS_REFERENCED", 1217, "Cannot delete or update a parent row: a foreign key constraint fails" },
{ "ER_CONNECT_TO_MASTER", 1218, "Error connecting to master: %-.128s" },
{ "ER_QUERY_ON_MASTER", 1219, "Error running query on master: %-.128s" },
{ "ER_ERROR_WHEN_EXECUTING_COMMAND", 1220, "Error when executing command %s: %-.128s" },
{ "ER_WRONG_USAGE", 1221, "Incorrect usage of %s and %s" },
{ "ER_WRONG_NUMBER_OF_COLUMNS_IN_SELECT", 1222, "The used SELECT statements have a different number of columns" },
{ "ER_CANT_UPDATE_WITH_READLOCK", 1223, "Can\'t execute the query because you have a conflicting read lock" },
{ "ER_MIXING_NOT_ALLOWED", 1224, "Mixing of transactional and non-transactional tables is disabled" },
{ "ER_DUP_ARGUMENT", 1225, "Option \'%s\' used twice in statement" },
{ "ER_USER_LIMIT_REACHED", 1226, "User \'%-.64s\' has exceeded the \'%s\' resource (current value: %ld)" },
{ "ER_SPECIFIC_ACCESS_DENIED_ERROR", 1227, "Access denied; you need (at least one of) the %-.128s privilege(s) for this operation" },
{ "ER_LOCAL_VARIABLE", 1228, "Variable \'%-.64s\' is a SESSION variable and can\'t be used with SET GLOBAL" },
{ "ER_GLOBAL_VARIABLE", 1229, "Variable \'%-.64s\' is a GLOBAL variable and should be set with SET GLOBAL" },
{ "ER_NO_DEFAULT", 1230, "Variable \'%-.64s\' doesn\'t have a default value" },
{ "ER_WRONG_VALUE_FOR_VAR", 1231, "Variable \'%-.64s\' can\'t be set to the value of \'%-.200s\'" },
{ "ER_WRONG_TYPE_FOR_VAR", 1232, "Incorrect argument type to variable \'%-.64s\'" },
{ "ER_VAR_CANT_BE_READ", 1233, "Variable \'%-.64s\' can only be set, not read" },
{ "ER_CANT_USE_OPTION_HERE", 1234, "Incorrect usage/placement of \'%s\'" },
{ "ER_NOT_SUPPORTED_YET", 1235, "This version of MySQL doesn\'t yet support \'%s\'" },
{ "ER_MASTER_FATAL_ERROR_READING_BINLOG", 1236, "Got fatal error %d from master when reading data from binary log: \'%-.320s\'" },
{ "ER_SLAVE_IGNORED_TABLE", 1237, "Slave SQL thread ignored the query because of replicate-*-table rules" },
{ "ER_INCORRECT_GLOBAL_LOCAL_VAR", 1238, "Variable \'%-.192s\' is a %s variable" },
{ "ER_WRONG_FK_DEF", 1239, "Incorrect foreign key definition for \'%-.192s\': %s" },
{ "ER_KEY_REF_DO_NOT_MATCH_TABLE_REF", 1240, "Key reference and table reference don\'t match" },
{ "ER_OPERAND_COLUMNS", 1241, "Operand should contain %d column(s)" },
{ "ER_SUBQUERY_NO_1_ROW", 1242, "Subquery returns more than 1 row" },
{ "ER_UNKNOWN_STMT_HANDLER", 1243, "Unknown prepared statement handler (%.*s) given to %s" },
{ "ER_CORRUPT_HELP_DB", 1244, "Help database is corrupt or does not exist" },
{ "ER_CYCLIC_REFERENCE", 1245, "Cyclic reference on subqueries" },
{ "ER_AUTO_CONVERT", 1246, "Converting column \'%s\' from %s to %s" },
{ "ER_ILLEGAL_REFERENCE", 1247, "Reference \'%-.64s\' not supported (%s)" },
{ "ER_DERIVED_MUST_HAVE_ALIAS", 1248, "Every derived table must have its own alias" },
{ "ER_SELECT_REDUCED", 1249, "Select %u was reduced during optimization" },
{ "ER_TABLENAME_NOT_ALLOWED_HERE", 1250, "Table \'%-.192s\' from one of the SELECTs cannot be used in %-.32s" },
{ "ER_NOT_SUPPORTED_AUTH_MODE", 1251, "Client does not support authentication protocol requested by server; consider upgrading MySQL client" },
{ "ER_SPATIAL_CANT_HAVE_NULL", 1252, "All parts of a SPATIAL index must be NOT NULL" },
{ "ER_COLLATION_CHARSET_MISMATCH", 1253, "COLLATION \'%s\' is not valid for CHARACTER SET \'%s\'" },
{ "ER_SLAVE_WAS_RUNNING", 1254, "Slave is already running" },
{ "ER_SLAVE_WAS_NOT_RUNNING", 1255, "Slave already has been stopped" },
{ "ER_TOO_BIG_FOR_UNCOMPRESS", 1256, "Uncompressed data size too large; the maximum size is %d (probably, length of uncompressed data was corrupted)" },
{ "ER_ZLIB_Z_MEM_ERROR", 1257, "ZLIB: Not enough memory" },
{ "ER_ZLIB_Z_BUF_ERROR", 1258, "ZLIB: Not enough room in the output buffer (probably, length of uncompressed data was corrupted)" },
{ "ER_ZLIB_Z_DATA_ERROR", 1259, "ZLIB: Input data corrupted" },
{ "ER_CUT_VALUE_GROUP_CONCAT", 1260, "Row %u was cut by GROUP_CONCAT()" },
{ "ER_WARN_TOO_FEW_RECORDS", 1261, "Row %ld doesn\'t contain data for all columns" },
{ "ER_WARN_TOO_MANY_RECORDS", 1262, "Row %ld was truncated; it contained more data than there were input columns" },
{ "ER_WARN_NULL_TO_NOTNULL", 1263, "Column set to default value; NULL supplied to NOT NULL column \'%s\' at row %ld" },
{ "ER_WARN_DATA_OUT_OF_RANGE", 1264, "Out of range value for column \'%s\' at row %ld" },
{ "WARN_DATA_TRUNCATED", 1265, "Data truncated for column \'%s\' at row %ld" },
{ "ER_WARN_USING_OTHER_HANDLER", 1266, "Using storage engine %s for table \'%s\'" },
{ "ER_CANT_AGGREGATE_2COLLATIONS", 1267, "Illegal mix of collations (%s,%s) and (%s,%s) for operation \'%s\'" },
{ "ER_DROP_USER", 1268, "Cannot drop one or more of the requested users" },
{ "ER_REVOKE_GRANTS", 1269, "Can\'t revoke all privileges for one or more of the requested users" },
{ "ER_CANT_AGGREGATE_3COLLATIONS", 1270, "Illegal mix of collations (%s,%s), (%s,%s), (%s,%s) for operation \'%s\'" },
{ "ER_CANT_AGGREGATE_NCOLLATIONS", 1271, "Illegal mix of collations for operation \'%s\'" },
{ "ER_VARIABLE_IS_NOT_STRUCT", 1272, "Variable \'%-.64s\' is not a variable component (can\'t be used as XXXX.variable_name)" },
{ "ER_UNKNOWN_COLLATION", 1273, "Unknown collation: \'%-.64s\'" },
{ "ER_SLAVE_IGNORED_SSL_PARAMS", 1274, "SSL parameters in CHANGE MASTER are ignored because this MySQL slave was compiled without SSL support; they can be used later if MySQL slave with SSL is started" },
{ "ER_SERVER_IS_IN_SECURE_AUTH_MODE", 1275, "Server is running in --secure-auth mode, but \'%s\'@\'%s\' has a password in the old format; please change the password to the new format" },
{ "ER_WARN_FIELD_RESOLVED", 1276, "Field or reference \'%-.192s%s%-.192s%s%-.192s\' of SELECT #%d was resolved in SELECT #%d" },
{ "ER_BAD_SLAVE_UNTIL_COND", 1277, "Incorrect parameter or combination of parameters for START SLAVE UNTIL" },
{ "ER_MISSING_SKIP_SLAVE", 1278, "It is recommended to use --skip-slave-start when doing step-by-step replication with START SLAVE UNTIL; otherwise, you will get problems if you get an unexpected slave\'s mysqld restart" },
{ "ER_UNTIL_COND_IGNORED", 1279, "SQL thread is not to be started so UNTIL options are ignored" },
{ "ER_WRONG_NAME_FOR_INDEX", 1280, "Incorrect index name \'%-.100s\'" },
{ "ER_WRONG_NAME_FOR_CATALOG", 1281, "Incorrect catalog name \'%-.100s\'" },
{ "ER_WARN_QC_RESIZE", 1282, "Query cache failed to set size %lu; new query cache size is %lu" },
{ "ER_BAD_FT_COLUMN", 1283, "Column \'%-.192s\' cannot be part of FULLTEXT index" },
{ "ER_UNKNOWN_KEY_CACHE", 1284, "Unknown key cache \'%-.100s\'" },
{ "ER_WARN_HOSTNAME_WONT_WORK", 1285, "MySQL is started in --skip-name-resolve mode; you must restart it without this switch for this grant to work" },
{ "ER_UNKNOWN_STORAGE_ENGINE", 1286, "Unknown storage engine \'%s\'" },
{ "ER_WARN_DEPRECATED_SYNTAX", 1287, "\'%s\' is deprecated and will be removed in a future release. Please use %s instead" },
{ "ER_NON_UPDATABLE_TABLE", 1288, "The target table %-.100s of the %s is not updatable" },
{ "ER_FEATURE_DISABLED", 1289, "The \'%s\' feature is disabled; you need MySQL built with \'%s\' to have it working" },
{ "ER_OPTION_PREVENTS_STATEMENT", 1290, "The MySQL server is running with the %s option so it cannot execute this statement" },
{ "ER_DUPLICATED_VALUE_IN_TYPE", 1291, "Column \'%-.100s\' has duplicated value \'%-.64s\' in %s" },
{ "ER_TRUNCATED_WRONG_VALUE", 1292, "Truncated incorrect %-.32s value: \'%-.128s\'" },
{ "ER_TOO_MUCH_AUTO_TIMESTAMP_COLS", 1293, "Incorrect table definition; there can be only one TIMESTAMP column with CURRENT_TIMESTAMP in DEFAULT or ON UPDATE clause" },
{ "ER_INVALID_ON_UPDATE", 1294, "Invalid ON UPDATE clause for \'%-.192s\' column" },
{ "ER_UNSUPPORTED_PS", 1295, "This command is not supported in the prepared statement protocol yet" },
{ "ER_GET_ERRMSG", 1296, "Got error %d \'%-.100s\' from %s" },
{ "ER_GET_TEMPORARY_ERRMSG", 1297, "Got temporary error %d \'%-.100s\' from %s" },
{ "ER_UNKNOWN_TIME_ZONE", 1298, "Unknown or incorrect time zone: \'%-.64s\'" },
{ "ER_WARN_INVALID_TIMESTAMP", 1299, "Invalid TIMESTAMP value in column \'%s\' at row %ld" },
{ "ER_INVALID_CHARACTER_STRING", 1300, "Invalid %s character string: \'%.64s\'" },
{ "ER_WARN_ALLOWED_PACKET_OVERFLOWED", 1301, "Result of %s() was larger than max_allowed_packet (%ld) - truncated" },
{ "ER_CONFLICTING_DECLARATIONS", 1302, "Conflicting declarations: \'%s%s\' and \'%s%s\'" },
{ "ER_SP_NO_RECURSIVE_CREATE", 1303, "Can\'t create a %s from within another stored routine" },
{ "ER_SP_ALREADY_EXISTS", 1304, "%s %s already exists" },
{ "ER_SP_DOES_NOT_EXIST", 1305, "%s %s does not exist" },
{ "ER_SP_DROP_FAILED", 1306, "Failed to DROP %s %s" },
{ "ER_SP_STORE_FAILED", 1307, "Failed to CREATE %s %s" },
{ "ER_SP_LILABEL_MISMATCH", 1308, "%s with no matching label: %s" },
{ "ER_SP_LABEL_REDEFINE", 1309, "Redefining label %s" },
{ "ER_SP_LABEL_MISMATCH", 1310, "End-label %s without match" },
{ "ER_SP_UNINIT_VAR", 1311, "Referring to uninitialized variable %s" },
{ "ER_SP_BADSELECT", 1312, "PROCEDURE %s can\'t return a result set in the given context" },
{ "ER_SP_BADRETURN", 1313, "RETURN is only allowed in a FUNCTION" },
{ "ER_SP_BADSTATEMENT", 1314, "%s is not allowed in stored procedures" },
{ "ER_UPDATE_LOG_DEPRECATED_IGNORED", 1315, "The update log is deprecated and replaced by the binary log; SET SQL_LOG_UPDATE has been ignored." },
{ "ER_UPDATE_LOG_DEPRECATED_TRANSLATED", 1316, "The update log is deprecated and replaced by the binary log; SET SQL_LOG_UPDATE has been translated to SET SQL_LOG_BIN." },
{ "ER_QUERY_INTERRUPTED", 1317, "Query execution was interrupted" },
{ "ER_SP_WRONG_NO_OF_ARGS", 1318, "Incorrect number of arguments for %s %s; expected %u, got %u" },
{ "ER_SP_COND_MISMATCH", 1319, "Undefined CONDITION: %s" },
{ "ER_SP_NORETURN", 1320, "No RETURN found in FUNCTION %s" },
{ "ER_SP_NORETURNEND", 1321, "FUNCTION %s ended without RETURN" },
{ "ER_SP_BAD_CURSOR_QUERY", 1322, "Cursor statement must be a SELECT" },
{ "ER_SP_BAD_CURSOR_SELECT", 1323, "Cursor SELECT must not have INTO" },
{ "ER_SP_CURSOR_MISMATCH", 1324, "Undefined CURSOR: %s" },
{ "ER_SP_CURSOR_ALREADY_OPEN", 1325, "Cursor is already open" },
{ "ER_SP_CURSOR_NOT_OPEN", 1326, "Cursor is not open" },
{ "ER_SP_UNDECLARED_VAR", 1327, "Undeclared variable: %s" },
{ "ER_SP_WRONG_NO_OF_FETCH_ARGS", 1328, "Incorrect number of FETCH variables" },
{ "ER_SP_FETCH_NO_DATA", 1329, "No data - zero rows fetched, selected, or processed" },
{ "ER_SP_DUP_PARAM", 1330, "Duplicate parameter: %s" },
{ "ER_SP_DUP_VAR", 1331, "Duplicate variable: %s" },
{ "ER_SP_DUP_COND", 1332, "Duplicate condition: %s" },
{ "ER_SP_DUP_CURS", 1333, "Duplicate cursor: %s" },
{ "ER_SP_CANT_ALTER", 1334, "Failed to ALTER %s %s" },
{ "ER_SP_SUBSELECT_NYI", 1335, "Subquery value not supported" },
{ "ER_STMT_NOT_ALLOWED_IN_SF_OR_TRG", 1336, "%s is not allowed in stored function or trigger" },
{ "ER_SP_VARCOND_AFTER_CURSHNDLR", 1337, "Variable or condition declaration after cursor or handler declaration" },
{ "ER_SP_CURSOR_AFTER_HANDLER", 1338, "Cursor declaration after handler declaration" },
{ "ER_SP_CASE_NOT_FOUND", 1339, "Case not found for CASE statement" },
{ "ER_FPARSER_TOO_BIG_FILE", 1340, "Configuration file \'%-.192s\' is too big" },
{ "ER_FPARSER_BAD_HEADER", 1341, "Malformed file type header in file \'%-.192s\'" },
{ "ER_FPARSER_EOF_IN_COMMENT", 1342, "Unexpected end of file while parsing comment \'%-.200s\'" },
{ "ER_FPARSER_ERROR_IN_PARAMETER", 1343, "Error while parsing parameter \'%-.192s\' (line: \'%-.192s\')" },
{ "ER_FPARSER_EOF_IN_UNKNOWN_PARAMETER", 1344, "Unexpected end of file while skipping unknown parameter \'%-.192s\'" },
{ "ER_VIEW_NO_EXPLAIN", 1345, "EXPLAIN/SHOW can not be issued; lacking privileges for underlying table" },
{ "ER_FRM_UNKNOWN_TYPE", 1346, "File \'%-.192s\' has unknown type \'%-.64s\' in its header" },
{ "ER_WRONG_OBJECT", 1347, "\'%-.192s.%-.192s\' is not %s" },
{ "ER_NONUPDATEABLE_COLUMN", 1348, "Column \'%-.192s\' is not updatable" },
{ "ER_VIEW_SELECT_DERIVED_UNUSED", 1349, "View\'s SELECT contains a subquery in the FROM clause" },
{ "ER_VIEW_SELECT_CLAUSE", 1350, "View\'s SELECT contains a \'%s\' clause" },
{ "ER_VIEW_SELECT_VARIABLE", 1351, "View\'s SELECT contains a variable or parameter" },
{ "ER_VIEW_SELECT_TMPTABLE", 1352, "View\'s SELECT refers to a temporary table \'%-.192s\'" },
{ "ER_VIEW_WRONG_LIST", 1353, "View\'s SELECT and view\'s field list have different column counts" },
{ "ER_WARN_VIEW_MERGE", 1354, "View merge algorithm can\'t be used here for now (assumed undefined algorithm)" },
{ "ER_WARN_VIEW_WITHOUT_KEY", 1355, "View being updated does not have complete key of underlying table in it" },
{ "ER_VIEW_INVALID", 1356, "View \'%-.192s.%-.192s\' references invalid table(s) or column(s) or function(s) or definer/invoker of view lack rights to use them" },
{ "ER_SP_NO_DROP_SP", 1357, "Can\'t drop or alter a %s from within another stored routine" },
{ "ER_SP_GOTO_IN_HNDLR", 1358, "GOTO is not allowed in a stored procedure handler" },
{ "ER_TRG_ALREADY_EXISTS", 1359, "Trigger already exists" },
{ "ER_TRG_DOES_NOT_EXIST", 1360, "Trigger does not exist" },
{ "ER_TRG_ON_VIEW_OR_TEMP_TABLE", 1361, "Trigger\'s \'%-.192s\' is view or temporary table" },
{ "ER_TRG_CANT_CHANGE_ROW", 1362, "Updating of %s row is not allowed in %strigger" },
{ "ER_TRG_NO_SUCH_ROW_IN_TRG", 1363, "There is no %s row in %s trigger" },
{ "ER_NO_DEFAULT_FOR_FIELD", 1364, "Field \'%-.192s\' doesn\'t have a default value" },
{ "ER_DIVISION_BY_ZERO", 1365, "Division by 0" },
{ "ER_TRUNCATED_WRONG_VALUE_FOR_FIELD", 1366, "Incorrect %-.32s value: \'%-.128s\' for column \'%.192s\' at row %ld" },
{ "ER_ILLEGAL_VALUE_FOR_TYPE", 1367, "Illegal %s \'%-.192s\' value found during parsing" },
{ "ER_VIEW_NONUPD_CHECK", 1368, "CHECK OPTION on non-updatable view \'%-.192s.%-.192s\'" },
{ "ER_VIEW_CHECK_FAILED", 1369, "CHECK OPTION failed \'%-.192s.%-.192s\'" },
{ "ER_PROCACCESS_DENIED_ERROR", 1370, "%-.16s command denied to user \'%-.48s\'@\'%-.64s\' for routine \'%-.192s\'" },
{ "ER_RELAY_LOG_FAIL", 1371, "Failed purging old relay logs: %s" },
{ "ER_PASSWD_LENGTH", 1372, "Password hash should be a %d-digit hexadecimal number" },
{ "ER_UNKNOWN_TARGET_BINLOG", 1373, "Target log not found in binlog index" },
{ "ER_IO_ERR_LOG_INDEX_READ", 1374, "I/O error reading log index file" },
{ "ER_BINLOG_PURGE_PROHIBITED", 1375, "Server configuration does not permit binlog purge" },
{ "ER_FSEEK_FAIL", 1376, "Failed on fseek()" },
{ "ER_BINLOG_PURGE_FATAL_ERR", 1377, "Fatal error during log purge" },
{ "ER_LOG_IN_USE", 1378, "A purgeable log is in use, will not purge" },
{ "ER_LOG_PURGE_UNKNOWN_ERR", 1379, "Unknown error during log purge" },
{ "ER_RELAY_LOG_INIT", 1380, "Failed initializing relay log position: %s" },
{ "ER_NO_BINARY_LOGGING", 1381, "You are not using binary logging" },
{ "ER_RESERVED_SYNTAX", 1382, "The \'%-.64s\' syntax is reserved for purposes internal to the MySQL server" },
{ "ER_WSAS_FAILED", 1383, "WSAStartup Failed" },
{ "ER_DIFF_GROUPS_PROC", 1384, "Can\'t handle procedures with different groups yet" },
{ "ER_NO_GROUP_FOR_PROC", 1385, "Select must have a group with this procedure" },
{ "ER_ORDER_WITH_PROC", 1386, "Can\'t use ORDER clause with this procedure" },
{ "ER_LOGGING_PROHIBIT_CHANGING_OF", 1387, "Binary logging and replication forbid changing the global server %s" },
{ "ER_NO_FILE_MAPPING", 1388, "Can\'t map file: %-.200s, errno: %d" },
{ "ER_WRONG_MAGIC", 1389, "Wrong magic in %-.64s" },
{ "ER_PS_MANY_PARAM", 1390, "Prepared statement contains too many placeholders" },
{ "ER_KEY_PART_0", 1391, "Key part \'%-.192s\' length cannot be 0" },
{ "ER_VIEW_CHECKSUM", 1392, "View text checksum failed" },
{ "ER_VIEW_MULTIUPDATE", 1393, "Can not modify more than one base table through a join view \'%-.192s.%-.192s\'" },
{ "ER_VIEW_NO_INSERT_FIELD_LIST", 1394, "Can not insert into join view \'%-.192s.%-.192s\' without fields list" },
{ "ER_VIEW_DELETE_MERGE_VIEW", 1395, "Can not delete from join view \'%-.192s.%-.192s\'" },
{ "ER_CANNOT_USER", 1396, "Operation %s failed for %.256s" },
{ "ER_XAER_NOTA", 1397, "XAER_NOTA: Unknown XID" },
{ "ER_XAER_INVAL", 1398, "XAER_INVAL: Invalid arguments (or unsupported command)" },
{ "ER_XAER_RMFAIL", 1399, "XAER_RMFAIL: The command cannot be executed when global transaction is in the  %.64s state" },
{ "ER_XAER_OUTSIDE", 1400, "XAER_OUTSIDE: Some work is done outside global transaction" },
{ "ER_XAER_RMERR", 1401, "XAER_RMERR: Fatal error occurred in the transaction branch - check your data for consistency" },
{ "ER_XA_RBROLLBACK", 1402, "XA_RBROLLBACK: Transaction branch was rolled back" },
{ "ER_NONEXISTING_PROC_GRANT", 1403, "There is no such grant defined for user \'%-.48s\' on host \'%-.64s\' on routine \'%-.192s\'" },
{ "ER_PROC_AUTO_GRANT_FAIL", 1404, "Failed to grant EXECUTE and ALTER ROUTINE privileges" },
{ "ER_PROC_AUTO_REVOKE_FAIL", 1405, "Failed to revoke all privileges to dropped routine" },
{ "ER_DATA_TOO_LONG", 1406, "Data too long for column \'%s\' at row %ld" },
{ "ER_SP_BAD_SQLSTATE", 1407, "Bad SQLSTATE: \'%s\'" },
{ "ER_STARTUP", 1408, "%s: ready for connections.\nVersion: \'%s\'  socket: \'%s\'  port: %d  %s" },
{ "ER_LOAD_FROM_FIXED_SIZE_ROWS_TO_VAR", 1409, "Can\'t load value from file with fixed size rows to variable" },
{ "ER_CANT_CREATE_USER_WITH_GRANT", 1410, "You are not allowed to create a user with GRANT" },
{ "ER_WRONG_VALUE_FOR_TYPE", 1411, "Incorrect %-.32s value: \'%-.128s\' for function %-.32s" },
{ "ER_TABLE_DEF_CHANGED", 1412, "Table definition has changed, please retry transaction" },
{ "ER_SP_DUP_HANDLER", 1413, "Duplicate handler declared in the same block" },
{ "ER_SP_NOT_VAR_ARG", 1414, "OUT or INOUT argument %d for routine %s is not a variable or NEW pseudo-variable in BEFORE trigger" },
{ "ER_SP_NO_RETSET", 1415, "Not allowed to return a result set from a %s" },
{ "ER_CANT_CREATE_GEOMETRY_OBJECT", 1416, "Cannot get geometry object from data you send to the GEOMETRY field" },
{ "ER_FAILED_ROUTINE_BREAK_BINLOG", 1417, "A routine failed and has neither NO SQL nor READS SQL DATA in its declaration and binary logging is enabled; if non-transactional tables were updated, the binary log will miss their changes" },
{ "ER_BINLOG_UNSAFE_ROUTINE", 1418, "This function has none of DETERMINISTIC, NO SQL, or READS SQL DATA in its declaration and binary logging is enabled (you *might* want to use the less safe log_bin_trust_function_creators variable)" },
{ "ER_BINLOG_CREATE_ROUTINE_NEED_SUPER", 1419, "You do not have the SUPER privilege and binary logging is enabled (you *might* want to use the less safe log_bin_trust_function_creators variable)" },
{ "ER_EXEC_STMT_WITH_OPEN_CURSOR", 1420, "You can\'t execute a prepared statement which has an open cursor associated with it. Reset the statement to re-execute it." },
{ "ER_STMT_HAS_NO_OPEN_CURSOR", 1421, "The statement (%lu) has no open cursor." },
{ "ER_COMMIT_NOT_ALLOWED_IN_SF_OR_TRG", 1422, "Explicit or implicit commit is not allowed in stored function or trigger." },
{ "ER_NO_DEFAULT_FOR_VIEW_FIELD", 1423, "Field of view \'%-.192s.%-.192s\' underlying table doesn\'t have a default value" },
{ "ER_SP_NO_RECURSION", 1424, "Recursive stored functions and triggers are not allowed." },
{ "ER_TOO_BIG_SCALE", 1425, "Too big scale %d specified for column \'%-.192s\'. Maximum is %lu." },
{ "ER_TOO_BIG_PRECISION", 1426, "Too-big precision %d specified for \'%-.192s\'. Maximum is %lu." },
{ "ER_M_BIGGER_THAN_D", 1427, "For float(M,D), double(M,D) or decimal(M,D), M must be >= D (column \'%-.192s\')." },
{ "ER_WRONG_LOCK_OF_SYSTEM_TABLE", 1428, "You can\'t combine write-locking of system tables with other tables or lock types" },
{ "ER_CONNECT_TO_FOREIGN_DATA_SOURCE", 1429, "Unable to connect to foreign data source: %.64s" },
{ "ER_QUERY_ON_FOREIGN_DATA_SOURCE", 1430, "There was a problem processing the query on the foreign data source. Data source error: %-.64s" },
{ "ER_FOREIGN_DATA_SOURCE_DOESNT_EXIST", 1431, "The foreign data source you are trying to reference does not exist. Data source error:  %-.64s" },
{ "ER_FOREIGN_DATA_STRING_INVALID_CANT_CREATE", 1432, "Can\'t create federated table. The data source connection string \'%-.64s\' is not in the correct format" },
{ "ER_FOREIGN_DATA_STRING_INVALID", 1433, "The data source connection string \'%-.64s\' is not in the correct format" },
{ "ER_CANT_CREATE_FEDERATED_TABLE", 1434, "Can\'t create federated table. Foreign data src error:  %-.64s" },
{ "ER_TRG_IN_WRONG_SCHEMA", 1435, "Trigger in wrong schema" },
{ "ER_STACK_OVERRUN_NEED_MORE", 1436, "Thread stack overrun:  %ld bytes used of a %ld byte stack, and %ld bytes needed.  Use \'mysqld --thread_stack=#\' to specify a bigger stack." },
{ "ER_TOO_LONG_BODY", 1437, "Routine body for \'%-.100s\' is too long" },
{ "ER_WARN_CANT_DROP_DEFAULT_KEYCACHE", 1438, "Cannot drop default keycache" },
{ "ER_TOO_BIG_DISPLAYWIDTH", 1439, "Display width out of range for column \'%-.192s\' (max = %lu)" },
{ "ER_XAER_DUPID", 1440, "XAER_DUPID: The XID already exists" },
{ "ER_DATETIME_FUNCTION_OVERFLOW", 1441, "Datetime function: %-.32s field overflow" },
{ "ER_CANT_UPDATE_USED_TABLE_IN_SF_OR_TRG", 1442, "Can\'t update table \'%-.192s\' in stored function/trigger because it is already used by statement which invoked this stored function/trigger." },
{ "ER_VIEW_PREVENT_UPDATE", 1443, "The definition of table \'%-.192s\' prevents operation %.192s on table \'%-.192s\'." },
{ "ER_PS_NO_RECURSION", 1444, "The prepared statement contains a stored routine call that refers to that same statement. It\'s not allowed to execute a prepared statement in such a recursive manner" },
{ "ER_SP_CANT_SET_AUTOCOMMIT", 1445, "Not allowed to set autocommit from a stored function or trigger" },
{ "ER_MALFORMED_DEFINER", 1446, "Definer is not fully qualified" },
{ "ER_VIEW_FRM_NO_USER", 1447, "View \'%-.192s\'.\'%-.192s\' has no definer information (old table format). Current user is used as definer. Please recreate the view!" },
{ "ER_VIEW_OTHER_USER", 1448, "You need the SUPER privilege for creation view with \'%-.192s\'@\'%-.192s\' definer" },
{ "ER_NO_SUCH_USER", 1449, "The user specified as a definer (\'%-.64s\'@\'%-.64s\') does not exist" },
{ "ER_FORBID_SCHEMA_CHANGE", 1450, "Changing schema from \'%-.192s\' to \'%-.192s\' is not allowed." },
{ "ER_ROW_IS_REFERENCED_2", 1451, "Cannot delete or update a parent row: a foreign key constraint fails (%.192s)" },
{ "ER_NO_REFERENCED_ROW_2", 1452, "Cannot add or update a child row: a foreign key constraint fails (%.192s)" },
{ "ER_SP_BAD_VAR_SHADOW", 1453, "Variable \'%-.64s\' must be quoted with `...`, or renamed" },
{ "ER_TRG_NO_DEFINER", 1454, "No definer attribute for trigger \'%-.192s\'.\'%-.192s\'. The trigger will be activated under the authorization of the invoker, which may have insufficient privileges. Please recreate the trigger." },
{ "ER_OLD_FILE_FORMAT", 1455, "\'%-.192s\' has an old format, you should re-create the \'%s\' object(s)" },
{ "ER_SP_RECURSION_LIMIT", 1456, "Recursive limit %d (as set by the max_sp_recursion_depth variable) was exceeded for routine %.192s" },
{ "ER_SP_PROC_TABLE_CORRUPT", 1457, "Failed to load routine %-.192s. The table mysql.proc is missing, corrupt, or contains bad data (internal code %d)" },
{ "ER_SP_WRONG_NAME", 1458, "Incorrect routine name \'%-.192s\'" },
{ "ER_TABLE_NEEDS_UPGRADE", 1459, "Table upgrade required. Please do \"REPAIR TABLE `%-.64s`\" or dump/reload to fix it!" },
{ "ER_SP_NO_AGGREGATE", 1460, "AGGREGATE is not supported for stored functions" },
{ "ER_MAX_PREPARED_STMT_COUNT_REACHED", 1461, "Can\'t create more than max_prepared_stmt_count statements (current value: %lu)" },
{ "ER_VIEW_RECURSIVE", 1462, "`%-.192s`.`%-.192s` contains view recursion" },
{ "ER_NON_GROUPING_FIELD_USED", 1463, "Non-grouping field \'%-.192s\' is used in %-.64s clause" },
{ "ER_TABLE_CANT_HANDLE_SPKEYS", 1464, "The used table type doesn\'t support SPATIAL indexes" },
{ "ER_NO_TRIGGERS_ON_SYSTEM_SCHEMA", 1465, "Triggers can not be created on system tables" },
{ "ER_REMOVED_SPACES", 1466, "Leading spaces are removed from name \'%s\'" },
{ "ER_AUTOINC_READ_FAILED", 1467, "Failed to read auto-increment value from storage engine" },
{ "ER_USERNAME", 1468, "user name" },
{ "ER_HOSTNAME", 1469, "host name" },
{ "ER_WRONG_STRING_LENGTH", 1470, "String \'%-.70s\' is too long for %s (should be no longer than %d)" },
{ "ER_NON_INSERTABLE_TABLE", 1471, "The target table %-.100s of the %s is not insertable-into" },
{ "ER_ADMIN_WRONG_MRG_TABLE", 1472, "Table \'%-.64s\' is differently defined or of non-MyISAM type or doesn\'t exist" },
{ "ER_TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT", 1473, "Too high level of nesting for select" },
{ "ER_NAME_BECOMES_EMPTY", 1474, "Name \'%-.64s\' has become \'\'" },
{ "ER_AMBIGUOUS_FIELD_TERM", 1475, "First character of the FIELDS TERMINATED string is ambiguous; please use non-optional and non-empty FIELDS ENCLOSED BY" },
{ "ER_FOREIGN_SERVER_EXISTS", 1476, "The foreign server, %s, you are trying to create already exists." },
{ "ER_FOREIGN_SERVER_DOESNT_EXIST", 1477, "The foreign server name you are trying to reference does not exist. Data source error:  %-.64s" },
{ "ER_ILLEGAL_HA_CREATE_OPTION", 1478, "Table storage engine \'%-.64s\' does not support the create option \'%.64s\'" },
{ "ER_PARTITION_REQUIRES_VALUES_ERROR", 1479, "Syntax error: %-.64s PARTITIONING requires definition of VALUES %-.64s for each partition" },
{ "ER_PARTITION_WRONG_VALUES_ERROR", 1480, "Only %-.64s PARTITIONING can use VALUES %-.64s in partition definition" },
{ "ER_PARTITION_MAXVALUE_ERROR", 1481, "MAXVALUE can only be used in last partition definition" },
{ "ER_PARTITION_SUBPARTITION_ERROR", 1482, "Subpartitions can only be hash partitions and by key" },
{ "ER_PARTITION_SUBPART_MIX_ERROR", 1483, "Must define subpartitions on all partitions if on one partition" },
{ "ER_PARTITION_WRONG_NO_PART_ERROR", 1484, "Wrong number of partitions defined, mismatch with previous setting" },
{ "ER_PARTITION_WRONG_NO_SUBPART_ERROR", 1485, "Wrong number of subpartitions defined, mismatch with previous setting" },
{ "ER_WRONG_EXPR_IN_PARTITION_FUNC_ERROR", 1486, "Constant, random or timezone-dependent expressions in (sub)partitioning function are not allowed" },
{ "ER_NO_CONST_EXPR_IN_RANGE_OR_LIST_ERROR", 1487, "Expression in RANGE/LIST VALUES must be constant" },
{ "ER_FIELD_NOT_FOUND_PART_ERROR", 1488, "Field in list of fields for partition function not found in table" },
{ "ER_LIST_OF_FIELDS_ONLY_IN_HASH_ERROR", 1489, "List of fields is only allowed in KEY partitions" },
{ "ER_INCONSISTENT_PARTITION_INFO_ERROR", 1490, "The partition info in the frm file is not consistent with what can be written into the frm file" },
{ "ER_PARTITION_FUNC_NOT_ALLOWED_ERROR", 1491, "The %-.192s function returns the wrong type" },
{ "ER_PARTITIONS_MUST_BE_DEFINED_ERROR", 1492, "For %-.64s partitions each partition must be defined" },
{ "ER_RANGE_NOT_INCREASING_ERROR", 1493, "VALUES LESS THAN value must be strictly increasing for each partition" },
{ "ER_INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR", 1494, "VALUES value must be of same type as partition function" },
{ "ER_MULTIPLE_DEF_CONST_IN_LIST_PART_ERROR", 1495, "Multiple definition of same constant in list partitioning" },
{ "ER_PARTITION_ENTRY_ERROR", 1496, "Partitioning can not be used stand-alone in query" },
{ "ER_MIX_HANDLER_ERROR", 1497, "The mix of handlers in the partitions is not allowed in this version of MySQL" },
{ "ER_PARTITION_NOT_DEFINED_ERROR", 1498, "For the partitioned engine it is necessary to define all %-.64s" },
{ "ER_TOO_MANY_PARTITIONS_ERROR", 1499, "Too many partitions (including subpartitions) were defined" },
{ "ER_SUBPARTITION_ERROR", 1500, "It is only possible to mix RANGE/LIST partitioning with HASH/KEY partitioning for subpartitioning" },
{ "ER_CANT_CREATE_HANDLER_FILE", 1501, "Failed to create specific handler file" },
{ "ER_BLOB_FIELD_IN_PART_FUNC_ERROR", 1502, "A BLOB field is not allowed in partition function" },
{ "ER_UNIQUE_KEY_NEED_ALL_FIELDS_IN_PF", 1503, "A %-.192s must include all columns in the table\'s partitioning function" },
{ "ER_NO_PARTS_ERROR", 1504, "Number of %-.64s = 0 is not an allowed value" },
{ "ER_PARTITION_MGMT_ON_NONPARTITIONED", 1505, "Partition management on a not partitioned table is not possible" },
{ "ER_FOREIGN_KEY_ON_PARTITIONED", 1506, "Foreign keys are not yet supported in conjunction with partitioning" },
{ "ER_DROP_PARTITION_NON_EXISTENT", 1507, "Error in list of partitions to %-.64s" },
{ "ER_DROP_LAST_PARTITION", 1508, "Cannot remove all partitions, use DROP TABLE instead" },
{ "ER_COALESCE_ONLY_ON_HASH_PARTITION", 1509, "COALESCE PARTITION can only be used on HASH/KEY partitions" },
{ "ER_REORG_HASH_ONLY_ON_SAME_NO", 1510, "REORGANIZE PARTITION can only be used to reorganize partitions not to change their numbers" },
{ "ER_REORG_NO_PARAM_ERROR", 1511, "REORGANIZE PARTITION without parameters can only be used on auto-partitioned tables using HASH PARTITIONs" },
{ "ER_ONLY_ON_RANGE_LIST_PARTITION", 1512, "%-.64s PARTITION can only be used on RANGE/LIST partitions" },
{ "ER_ADD_PARTITION_SUBPART_ERROR", 1513, "Trying to Add partition(s) with wrong number of subpartitions" },
{ "ER_ADD_PARTITION_NO_NEW_PARTITION", 1514, "At least one partition must be added" },
{ "ER_COALESCE_PARTITION_NO_PARTITION", 1515, "At least one partition must be coalesced" },
{ "ER_REORG_PARTITION_NOT_EXIST", 1516, "More partitions to reorganize than there are partitions" },
{ "ER_SAME_NAME_PARTITION", 1517, "Duplicate partition name %-.192s" },
{ "ER_NO_BINLOG_ERROR", 1518, "It is not allowed to shut off binlog on this command" },
{ "ER_CONSECUTIVE_REORG_PARTITIONS", 1519, "When reorganizing a set of partitions they must be in consecutive order" },
{ "ER_REORG_OUTSIDE_RANGE", 1520, "Reorganize of range partitions cannot change total ranges except for last partition where it can extend the range" },
{ "ER_PARTITION_FUNCTION_FAILURE", 1521, "Partition function not supported in this version for this handler" },
{ "ER_PART_STATE_ERROR", 1522, "Partition state cannot be defined from CREATE/ALTER TABLE" },
{ "ER_LIMITED_PART_RANGE", 1523, "The %-.64s handler only supports 32 bit integers in VALUES" },
{ "ER_PLUGIN_IS_NOT_LOADED", 1524, "Plugin \'%-.192s\' is not loaded" },
{ "ER_WRONG_VALUE", 1525, "Incorrect %-.32s value: \'%-.128s\'" },
{ "ER_NO_PARTITION_FOR_GIVEN_VALUE", 1526, "Table has no partition for value %-.64s" },
{ "ER_FILEGROUP_OPTION_ONLY_ONCE", 1527, "It is not allowed to specify %s more than once" },
{ "ER_CREATE_FILEGROUP_FAILED", 1528, "Failed to create %s" },
{ "ER_DROP_FILEGROUP_FAILED", 1529, "Failed to drop %s" },
{ "ER_TABLESPACE_AUTO_EXTEND_ERROR", 1530, "The handler doesn\'t support autoextend of tablespaces" },
{ "ER_WRONG_SIZE_NUMBER", 1531, "A size parameter was incorrectly specified, either number or on the form 10M" },
{ "ER_SIZE_OVERFLOW_ERROR", 1532, "The size number was correct but we don\'t allow the digit part to be more than 2 billion" },
{ "ER_ALTER_FILEGROUP_FAILED", 1533, "Failed to alter: %s" },
{ "ER_BINLOG_ROW_LOGGING_FAILED", 1534, "Writing one row to the row-based binary log failed" },
{ "ER_BINLOG_ROW_WRONG_TABLE_DEF", 1535, "Table definition on master and slave does not match: %s" },
{ "ER_BINLOG_ROW_RBR_TO_SBR", 1536, "Slave running with --log-slave-updates must use row-based binary logging to be able to replicate row-based binary log events" },
{ "ER_EVENT_ALREADY_EXISTS", 1537, "Event \'%-.192s\' already exists" },
{ "ER_EVENT_STORE_FAILED", 1538, "Failed to store event %s. Error code %d from storage engine." },
{ "ER_EVENT_DOES_NOT_EXIST", 1539, "Unknown event \'%-.192s\'" },
{ "ER_EVENT_CANT_ALTER", 1540, "Failed to alter event \'%-.192s\'" },
{ "ER_EVENT_DROP_FAILED", 1541, "Failed to drop %s" },
{ "ER_EVENT_INTERVAL_NOT_POSITIVE_OR_TOO_BIG", 1542, "INTERVAL is either not positive or too big" },
{ "ER_EVENT_ENDS_BEFORE_STARTS", 1543, "ENDS is either invalid or before STARTS" },
{ "ER_EVENT_EXEC_TIME_IN_THE_PAST", 1544, "Event execution time is in the past. Event has been disabled" },
{ "ER_EVENT_OPEN_TABLE_FAILED", 1545, "Failed to open mysql.event" },
{ "ER_EVENT_NEITHER_M_EXPR_NOR_M_AT", 1546, "No datetime expression provided" },
{ "ER_OBSOLETE_COL_COUNT_DOESNT_MATCH_CORRUPTED", 1547, "Column count of mysql.%s is wrong. Expected %d, found %d. The table is probably corrupted" },
{ "ER_OBSOLETE_CANNOT_LOAD_FROM_TABLE", 1548, "Cannot load from mysql.%s. The table is probably corrupted" },
{ "ER_EVENT_CANNOT_DELETE", 1549, "Failed to delete the event from mysql.event" },
{ "ER_EVENT_COMPILE_ERROR", 1550, "Error during compilation of event\'s body" },
{ "ER_EVENT_SAME_NAME", 1551, "Same old and new event name" },
{ "ER_EVENT_DATA_TOO_LONG", 1552, "Data for column \'%s\' too long" },
{ "ER_DROP_INDEX_FK", 1553, "Cannot drop index \'%-.192s\': needed in a foreign key constraint" },
{ "ER_WARN_DEPRECATED_SYNTAX_WITH_VER", 1554, "The syntax \'%s\' is deprecated and will be removed in MySQL %s. Please use %s instead" },
{ "ER_CANT_WRITE_LOCK_LOG_TABLE", 1555, "You can\'t write-lock a log table. Only read access is possible" },
{ "ER_CANT_LOCK_LOG_TABLE", 1556, "You can\'t use locks with log tables." },
{ "ER_FOREIGN_DUPLICATE_KEY_OLD_UNUSED", 1557, "Upholding foreign key constraints for table \'%.192s\', entry \'%-.192s\', key %d would lead to a duplicate entry" },
{ "ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE", 1558, "Column count of mysql.%s is wrong. Expected %d, found %d. Created with MySQL %d, now running %d. Please use mysql_upgrade to fix this error." },
{ "ER_TEMP_TABLE_PREVENTS_SWITCH_OUT_OF_RBR", 1559, "Cannot switch out of the row-based binary log format when the session has open temporary tables" },
{ "ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_FORMAT", 1560, "Cannot change the binary logging format inside a stored function or trigger" },
{ "ER_NDB_CANT_SWITCH_BINLOG_FORMAT", 1561, "The NDB cluster engine does not support changing the binlog format on the fly yet" },
{ "ER_PARTITION_NO_TEMPORARY", 1562, "Cannot create temporary table with partitions" },
{ "ER_PARTITION_CONST_DOMAIN_ERROR", 1563, "Partition constant is out of partition function domain" },
{ "ER_PARTITION_FUNCTION_IS_NOT_ALLOWED", 1564, "This partition function is not allowed" },
{ "ER_DDL_LOG_ERROR", 1565, "Error in DDL log" },
{ "ER_NULL_IN_VALUES_LESS_THAN", 1566, "Not allowed to use NULL value in VALUES LESS THAN" },
{ "ER_WRONG_PARTITION_NAME", 1567, "Incorrect partition name" },
{ "ER_CANT_CHANGE_TX_CHARACTERISTICS", 1568, "Transaction characteristics can\'t be changed while a transaction is in progress" },
{ "ER_DUP_ENTRY_AUTOINCREMENT_CASE", 1569, "ALTER TABLE causes auto_increment resequencing, resulting in duplicate entry \'%-.192s\' for key \'%-.192s\'" },
{ "ER_EVENT_MODIFY_QUEUE_ERROR", 1570, "Internal scheduler error %d" },
{ "ER_EVENT_SET_VAR_ERROR", 1571, "Error during starting/stopping of the scheduler. Error code %u" },
{ "ER_PARTITION_MERGE_ERROR", 1572, "Engine cannot be used in partitioned tables" },
{ "ER_CANT_ACTIVATE_LOG", 1573, "Cannot activate \'%-.64s\' log" },
{ "ER_RBR_NOT_AVAILABLE", 1574, "The server was not built with row-based replication" },
{ "ER_BASE64_DECODE_ERROR", 1575, "Decoding of base64 string failed" },
{ "ER_EVENT_RECURSION_FORBIDDEN", 1576, "Recursion of EVENT DDL statements is forbidden when body is present" },
{ "ER_EVENTS_DB_ERROR", 1577, "Cannot proceed because system tables used by Event Scheduler were found damaged at server start" },
{ "ER_ONLY_INTEGERS_ALLOWED", 1578, "Only integers allowed as number here" },
{ "ER_UNSUPORTED_LOG_ENGINE", 1579, "This storage engine cannot be used for log tables\"" },
{ "ER_BAD_LOG_STATEMENT", 1580, "You cannot \'%s\' a log table if logging is enabled" },
{ "ER_CANT_RENAME_LOG_TABLE", 1581, "Cannot rename \'%s\'. When logging enabled, rename to/from log table must rename two tables: the log table to an archive table and another table back to \'%s\'" },
{ "ER_WRONG_PARAMCOUNT_TO_NATIVE_FCT", 1582, "Incorrect parameter count in the call to native function \'%-.192s\'" },
{ "ER_WRONG_PARAMETERS_TO_NATIVE_FCT", 1583, "Incorrect parameters in the call to native function \'%-.192s\'" },
{ "ER_WRONG_PARAMETERS_TO_STORED_FCT", 1584, "Incorrect parameters in the call to stored function %-.192s" },
{ "ER_NATIVE_FCT_NAME_COLLISION", 1585, "This function \'%-.192s\' has the same name as a native function" },
{ "ER_DUP_ENTRY_WITH_KEY_NAME", 1586, "Duplicate entry \'%-.64s\' for key \'%-.192s\'" },
{ "ER_BINLOG_PURGE_EMFILE", 1587, "Too many files opened, please execute the command again" },
{ "ER_EVENT_CANNOT_CREATE_IN_THE_PAST", 1588, "Event execution time is in the past and ON COMPLETION NOT PRESERVE is set. The event was dropped immediately after creation." },
{ "ER_EVENT_CANNOT_ALTER_IN_THE_PAST", 1589, "Event execution time is in the past and ON COMPLETION NOT PRESERVE is set. The event was not changed. Specify a time in the future." },
{ "ER_SLAVE_INCIDENT", 1590, "The incident %s occured on the master. Message: %s" },
{ "ER_NO_PARTITION_FOR_GIVEN_VALUE_SILENT", 1591, "Table has no partition for some existing values" },
{ "ER_BINLOG_UNSAFE_STATEMENT", 1592, "Unsafe statement written to the binary log using statement format since BINLOG_FORMAT = STATEMENT. %s" },
{ "ER_SLAVE_FATAL_ERROR", 1593, "Fatal error: %s" },
{ "ER_SLAVE_RELAY_LOG_READ_FAILURE", 1594, "Relay log read failure: %s" },
{ "ER_SLAVE_RELAY_LOG_WRITE_FAILURE", 1595, "Relay log write failure: %s" },
{ "ER_SLAVE_CREATE_EVENT_FAILURE", 1596, "Failed to create %s" },
{ "ER_SLAVE_MASTER_COM_FAILURE", 1597, "Master command %s failed: %s" },
{ "ER_BINLOG_LOGGING_IMPOSSIBLE", 1598, "Binary logging not possible. Message: %s" },
{ "ER_VIEW_NO_CREATION_CTX", 1599, "View `%-.64s`.`%-.64s` has no creation context" },
{ "ER_VIEW_INVALID_CREATION_CTX", 1600, "Creation context of view `%-.64s`.`%-.64s\' is invalid" },
{ "ER_SR_INVALID_CREATION_CTX", 1601, "Creation context of stored routine `%-.64s`.`%-.64s` is invalid" },
{ "ER_TRG_CORRUPTED_FILE", 1602, "Corrupted TRG file for table `%-.64s`.`%-.64s`" },
{ "ER_TRG_NO_CREATION_CTX", 1603, "Triggers for table `%-.64s`.`%-.64s` have no creation context" },
{ "ER_TRG_INVALID_CREATION_CTX", 1604, "Trigger creation context of table `%-.64s`.`%-.64s` is invalid" },
{ "ER_EVENT_INVALID_CREATION_CTX", 1605, "Creation context of event `%-.64s`.`%-.64s` is invalid" },
{ "ER_TRG_CANT_OPEN_TABLE", 1606, "Cannot open table for trigger `%-.64s`.`%-.64s`" },
{ "ER_CANT_CREATE_SROUTINE", 1607, "Cannot create stored routine `%-.64s`. Check warnings" },
{ "ER_NEVER_USED", 1608, "Ambiguous slave modes combination. %s" },
{ "ER_NO_FORMAT_DESCRIPTION_EVENT_BEFORE_BINLOG_STATEMENT", 1609, "The BINLOG statement of type `%s` was not preceded by a format description BINLOG statement." },
{ "ER_SLAVE_CORRUPT_EVENT", 1610, "Corrupted replication event was detected" },
{ "ER_LOAD_DATA_INVALID_COLUMN_UNUSED", 1611, "Invalid column reference (%-.64s) in LOAD DATA" },
{ "ER_LOG_PURGE_NO_FILE", 1612, "Being purged log %s was not found" },
{ "ER_XA_RBTIMEOUT", 1613, "XA_RBTIMEOUT: Transaction branch was rolled back: took too long" },
{ "ER_XA_RBDEADLOCK", 1614, "XA_RBDEADLOCK: Transaction branch was rolled back: deadlock was detected" },
{ "ER_NEED_REPREPARE", 1615, "Prepared statement needs to be re-prepared" },
{ "ER_DELAYED_NOT_SUPPORTED", 1616, "DELAYED option not supported for table \'%-.192s\'" },
{ "WARN_NO_MASTER_INFO", 1617, "The master info structure does not exist" },
{ "WARN_OPTION_IGNORED", 1618, "<%-.64s> option ignored" },
{ "ER_PLUGIN_DELETE_BUILTIN", 1619, "Built-in plugins cannot be deleted" },
{ "WARN_PLUGIN_BUSY", 1620, "Plugin is busy and will be uninstalled on shutdown" },
{ "ER_VARIABLE_IS_READONLY", 1621, "%s variable \'%s\' is read-only. Use SET %s to assign the value" },
{ "ER_WARN_ENGINE_TRANSACTION_ROLLBACK", 1622, "Storage engine %s does not support rollback for this statement. Transaction rolled back and must be restarted" },
{ "ER_SLAVE_HEARTBEAT_FAILURE", 1623, "Unexpected master\'s heartbeat data: %s" },
{ "ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE", 1624, "The requested value for the heartbeat period is either negative or exceeds the maximum allowed (%s seconds)." },
{ "ER_NDB_REPLICATION_SCHEMA_ERROR", 1625, "Bad schema for mysql.ndb_replication table. Message: %-.64s" },
{ "ER_CONFLICT_FN_PARSE_ERROR", 1626, "Error in parsing conflict function. Message: %-.64s" },
{ "ER_EXCEPTIONS_WRITE_ERROR", 1627, "Write to exceptions table failed. Message: %-.128s\"" },
{ "ER_TOO_LONG_TABLE_COMMENT", 1628, "Comment for table \'%-.64s\' is too long (max = %lu)" },
{ "ER_TOO_LONG_FIELD_COMMENT", 1629, "Comment for field \'%-.64s\' is too long (max = %lu)" },
{ "ER_FUNC_INEXISTENT_NAME_COLLISION", 1630, "FUNCTION %s does not exist. Check the \'Function Name Parsing and Resolution\' section in the Reference Manual" },
{ "ER_DATABASE_NAME", 1631, "Database" },
{ "ER_TABLE_NAME", 1632, "Table" },
{ "ER_PARTITION_NAME", 1633, "Partition" },
{ "ER_SUBPARTITION_NAME", 1634, "Subpartition" },
{ "ER_TEMPORARY_NAME", 1635, "Temporary" },
{ "ER_RENAMED_NAME", 1636, "Renamed" },
{ "ER_TOO_MANY_CONCURRENT_TRXS", 1637, "Too many active concurrent transactions" },
{ "WARN_NON_ASCII_SEPARATOR_NOT_IMPLEMENTED", 1638, "Non-ASCII separator arguments are not fully supported" },
{ "ER_DEBUG_SYNC_TIMEOUT", 1639, "debug sync point wait timed out" },
{ "ER_DEBUG_SYNC_HIT_LIMIT", 1640, "debug sync point hit limit reached" },
{ "ER_DUP_SIGNAL_SET", 1641, "Duplicate condition information item \'%s\'" },
{ "ER_SIGNAL_WARN", 1642, "Unhandled user-defined warning condition" },
{ "ER_SIGNAL_NOT_FOUND", 1643, "Unhandled user-defined not found condition" },
{ "ER_SIGNAL_EXCEPTION", 1644, "Unhandled user-defined exception condition" },
{ "ER_RESIGNAL_WITHOUT_ACTIVE_HANDLER", 1645, "RESIGNAL when handler not active" },
{ "ER_SIGNAL_BAD_CONDITION_TYPE", 1646, "SIGNAL/RESIGNAL can only use a CONDITION defined with SQLSTATE" },
{ "WARN_COND_ITEM_TRUNCATED", 1647, "Data truncated for condition item \'%s\'" },
{ "ER_COND_ITEM_TOO_LONG", 1648, "Data too long for condition item \'%s\'" },
{ "ER_UNKNOWN_LOCALE", 1649, "Unknown locale: \'%-.64s\'" },
{ "ER_SLAVE_IGNORE_SERVER_IDS", 1650, "The requested server id %d clashes with the slave startup option --replicate-same-server-id" },
{ "ER_QUERY_CACHE_DISABLED", 1651, "Query cache is disabled; restart the server with query_cache_type=1 to enable it" },
{ "ER_SAME_NAME_PARTITION_FIELD", 1652, "Duplicate partition field name \'%-.192s\'" },
{ "ER_PARTITION_COLUMN_LIST_ERROR", 1653, "Inconsistency in usage of column lists for partitioning" },
{ "ER_WRONG_TYPE_COLUMN_VALUE_ERROR", 1654, "Partition column values of incorrect type" },
{ "ER_TOO_MANY_PARTITION_FUNC_FIELDS_ERROR", 1655, "Too many fields in \'%-.192s\'" },
{ "ER_MAXVALUE_IN_VALUES_IN", 1656, "Cannot use MAXVALUE as value in VALUES IN" },
{ "ER_TOO_MANY_VALUES_ERROR", 1657, "Cannot have more than one value for this type of %-.64s partitioning" },
{ "ER_ROW_SINGLE_PARTITION_FIELD_ERROR", 1658, "Row expressions in VALUES IN only allowed for multi-field column partitioning" },
{ "ER_FIELD_TYPE_NOT_ALLOWED_AS_PARTITION_FIELD", 1659, "Field \'%-.192s\' is of a not allowed type for this type of partitioning" },
{ "ER_PARTITION_FIELDS_TOO_LONG", 1660, "The total length of the partitioning fields is too large" },
{ "ER_BINLOG_ROW_ENGINE_AND_STMT_ENGINE", 1661, "Cannot execute statement: impossible to write to binary log since both row-incapable engines and statement-incapable engines are involved." },
{ "ER_BINLOG_ROW_MODE_AND_STMT_ENGINE", 1662, "Cannot execute statement: impossible to write to binary log since BINLOG_FORMAT = ROW and at least one table uses a storage engine limited to statement-based logging." },
{ "ER_BINLOG_UNSAFE_AND_STMT_ENGINE", 1663, "Cannot execute statement: impossible to write to binary log since statement is unsafe, storage engine is limited to statement-based logging, and BINLOG_FORMAT = MIXED. %s" },
{ "ER_BINLOG_ROW_INJECTION_AND_STMT_ENGINE", 1664, "Cannot execute statement: impossible to write to binary log since statement is in row format and at least one table uses a storage engine limited to statement-based logging." },
{ "ER_BINLOG_STMT_MODE_AND_ROW_ENGINE", 1665, "Cannot execute statement: impossible to write to binary log since BINLOG_FORMAT = STATEMENT and at least one table uses a storage engine limited to row-based logging.%s" },
{ "ER_BINLOG_ROW_INJECTION_AND_STMT_MODE", 1666, "Cannot execute statement: impossible to write to binary log since statement is in row format and BINLOG_FORMAT = STATEMENT." },
{ "ER_BINLOG_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE", 1667, "Cannot execute statement: impossible to write to binary log since more than one engine is involved and at least one engine is self-logging." },
{ "ER_BINLOG_UNSAFE_LIMIT", 1668, "The statement is unsafe because it uses a LIMIT clause. This is unsafe because the set of rows included cannot be predicted." },
{ "ER_UNUSED4", 1669, "The statement is unsafe because it uses INSERT DELAYED. This is unsafe because the times when rows are inserted cannot be predicted." },
{ "ER_BINLOG_UNSAFE_SYSTEM_TABLE", 1670, "The statement is unsafe because it uses the general log, slow query log, or performance_schema table(s). This is unsafe because system tables may differ on slaves." },
{ "ER_BINLOG_UNSAFE_AUTOINC_COLUMNS", 1671, "Statement is unsafe because it invokes a trigger or a stored function that inserts into an AUTO_INCREMENT column. Inserted values cannot be logged correctly." },
{ "ER_BINLOG_UNSAFE_UDF", 1672, "Statement is unsafe because it uses a UDF which may not return the same value on the slave." },
{ "ER_BINLOG_UNSAFE_SYSTEM_VARIABLE", 1673, "Statement is unsafe because it uses a system variable that may have a different value on the slave." },
{ "ER_BINLOG_UNSAFE_SYSTEM_FUNCTION", 1674, "Statement is unsafe because it uses a system function that may return a different value on the slave." },
{ "ER_BINLOG_UNSAFE_NONTRANS_AFTER_TRANS", 1675, "Statement is unsafe because it accesses a non-transactional table after accessing a transactional table within the same transaction." },
{ "ER_MESSAGE_AND_STATEMENT", 1676, "%s Statement: %s" },
{ "ER_SLAVE_CONVERSION_FAILED", 1677, "Column %d of table \'%-.192s.%-.192s\' cannot be converted from type \'%-.32s\' to type \'%-.32s\'" },
{ "ER_SLAVE_CANT_CREATE_CONVERSION", 1678, "Can\'t create conversion table for table \'%-.192s.%-.192s\'" },
{ "ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_FORMAT", 1679, "Cannot modify @@session.binlog_format inside a transaction" },
{ "ER_PATH_LENGTH", 1680, "The path specified for %.64s is too long." },
{ "ER_WARN_DEPRECATED_SYNTAX_NO_REPLACEMENT", 1681, "\'%s\' is deprecated and will be removed in a future release." },
{ "ER_WRONG_NATIVE_TABLE_STRUCTURE", 1682, "Native table \'%-.64s\'.\'%-.64s\' has the wrong structure" },
{ "ER_WRONG_PERFSCHEMA_USAGE", 1683, "Invalid performance_schema usage." },
{ "ER_WARN_I_S_SKIPPED_TABLE", 1684, "Table \'%s\'.\'%s\' was skipped since its definition is being modified by concurrent DDL statement" },
{ "ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_DIRECT", 1685, "Cannot modify @@session.binlog_direct_non_transactional_updates inside a transaction" },
{ "ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_DIRECT", 1686, "Cannot change the binlog direct flag inside a stored function or trigger" },
{ "ER_SPATIAL_MUST_HAVE_GEOM_COL", 1687, "A SPATIAL index may only contain a geometrical type column" },
{ "ER_TOO_LONG_INDEX_COMMENT", 1688, "Comment for index \'%-.64s\' is too long (max = %lu)" },
{ "ER_LOCK_ABORTED", 1689, "Wait on a lock was aborted due to a pending exclusive lock" },
{ "ER_DATA_OUT_OF_RANGE", 1690, "%s value is out of range in \'%s\'" },
{ "ER_WRONG_SPVAR_TYPE_IN_LIMIT", 1691, "A variable of a non-integer based type in LIMIT clause" },
{ "ER_BINLOG_UNSAFE_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE", 1692, "Mixing self-logging and non-self-logging engines in a statement is unsafe." },
{ "ER_BINLOG_UNSAFE_MIXED_STATEMENT", 1693, "Statement accesses nontransactional table as well as transactional or temporary table, and writes to any of them." },
{ "ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_SQL_LOG_BIN", 1694, "Cannot modify @@session.sql_log_bin inside a transaction" },
{ "ER_STORED_FUNCTION_PREVENTS_SWITCH_SQL_LOG_BIN", 1695, "Cannot change the sql_log_bin inside a stored function or trigger" },
{ "ER_FAILED_READ_FROM_PAR_FILE", 1696, "Failed to read from the .par file" },
{ "ER_VALUES_IS_NOT_INT_TYPE_ERROR", 1697, "VALUES value for partition \'%-.64s\' must have type INT" },
{ "ER_ACCESS_DENIED_NO_PASSWORD_ERROR", 1698, "Access denied for user \'%-.48s\'@\'%-.64s\'" },
{ "ER_SET_PASSWORD_AUTH_PLUGIN", 1699, "SET PASSWORD has no significance for users authenticating via plugins" },
{ "ER_GRANT_PLUGIN_USER_EXISTS", 1700, "GRANT with IDENTIFIED WITH is illegal because the user %-.*s already exists" },
{ "ER_TRUNCATE_ILLEGAL_FK", 1701, "Cannot truncate a table referenced in a foreign key constraint (%.192s)" },
{ "ER_PLUGIN_IS_PERMANENT", 1702, "Plugin \'%s\' is force_plus_permanent and can not be unloaded" },
{ "ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MIN", 1703, "The requested value for the heartbeat period is less than 1 millisecond. The value is reset to 0, meaning that heartbeating will effectively be disabled." },
{ "ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MAX", 1704, "The requested value for the heartbeat period exceeds the value of `slave_net_timeout\' seconds. A sensible value for the period should be less than the timeout." },
{ "ER_STMT_CACHE_FULL", 1705, "Multi-row statements required more than \'max_binlog_stmt_cache_size\' bytes of storage; increase this mysqld variable and try again" },
{ "ER_MULTI_UPDATE_KEY_CONFLICT", 1706, "Primary key/partition key update is not allowed since the table is updated both as \'%-.192s\' and \'%-.192s\'." },
{ "ER_TABLE_NEEDS_REBUILD", 1707, "Table rebuild required. Please do \"ALTER TABLE `%-.64s` FORCE\" or dump/reload to fix it!" },
{ "WARN_OPTION_BELOW_LIMIT", 1708, "The value of \'%s\' should be no less than the value of \'%s\'" },
{ "ER_INDEX_COLUMN_TOO_LONG", 1709, "Index column size too large. The maximum column size is %lu bytes." },
{ "ER_ERROR_IN_TRIGGER_BODY", 1710, "Trigger \'%-.64s\' has an error in its body: \'%-.256s\'" },
{ "ER_ERROR_IN_UNKNOWN_TRIGGER_BODY", 1711, "Unknown trigger has an error in its body: \'%-.256s\'" },
{ "ER_INDEX_CORRUPT", 1712, "Index %s is corrupted" },
{ "ER_UNDO_RECORD_TOO_BIG", 1713, "Undo log record is too big." },
{ "ER_BINLOG_UNSAFE_INSERT_IGNORE_SELECT", 1714, "INSERT IGNORE... SELECT is unsafe because the order in which rows are retrieved by the SELECT determines which (if any) rows are ignored. This order cannot be predicted and may differ on master and the slave." },
{ "ER_BINLOG_UNSAFE_INSERT_SELECT_UPDATE", 1715, "INSERT... SELECT... ON DUPLICATE KEY UPDATE is unsafe because the order in which rows are retrieved by the SELECT determines which (if any) rows are updated. This order cannot be predicted and may differ on master and the slave." },
{ "ER_BINLOG_UNSAFE_REPLACE_SELECT", 1716, "REPLACE... SELECT is unsafe because the order in which rows are retrieved by the SELECT determines which (if any) rows are replaced. This order cannot be predicted and may differ on master and the slave." },
{ "ER_BINLOG_UNSAFE_CREATE_IGNORE_SELECT", 1717, "CREATE... IGNORE SELECT is unsafe because the order in which rows are retrieved by the SELECT determines which (if any) rows are ignored. This order cannot be predicted and may differ on master and the slave." },
{ "ER_BINLOG_UNSAFE_CREATE_REPLACE_SELECT", 1718, "CREATE... REPLACE SELECT is unsafe because the order in which rows are retrieved by the SELECT determines which (if any) rows are replaced. This order cannot be predicted and may differ on master and the slave." },
{ "ER_BINLOG_UNSAFE_UPDATE_IGNORE", 1719, "UPDATE IGNORE is unsafe because the order in which rows are updated determines which (if any) rows are ignored. This order cannot be predicted and may differ on master and the slave." },
{ "ER_PLUGIN_NO_UNINSTALL", 1720, "Plugin \'%s\' is marked as not dynamically uninstallable. You have to stop the server to uninstall it." },
{ "ER_PLUGIN_NO_INSTALL", 1721, "Plugin \'%s\' is marked as not dynamically installable. You have to stop the server to install it." },
{ "ER_BINLOG_UNSAFE_WRITE_AUTOINC_SELECT", 1722, "Statements writing to a table with an auto-increment column after selecting from another table are unsafe because the order in which rows are retrieved determines what (if any) rows will be written. This order cannot be predicted and may differ on master and the slave." },
{ "ER_BINLOG_UNSAFE_CREATE_SELECT_AUTOINC", 1723, "CREATE TABLE... SELECT...  on a table with an auto-increment column is unsafe because the order in which rows are retrieved by the SELECT determines which (if any) rows are inserted. This order cannot be predicted and may differ on master and the slave." },
{ "ER_BINLOG_UNSAFE_INSERT_TWO_KEYS", 1724, "INSERT... ON DUPLICATE KEY UPDATE  on a table with more than one UNIQUE KEY is unsafe" },
{ "ER_TABLE_IN_FK_CHECK", 1725, "Table is being used in foreign key check." },
{ "ER_UNSUPPORTED_ENGINE", 1726, "Storage engine \'%s\' does not support system tables. [%s.%s]" },
{ "ER_BINLOG_UNSAFE_AUTOINC_NOT_FIRST", 1727, "INSERT into autoincrement field which is not the first part in the composed primary key is unsafe." },
{ "ER_CANNOT_LOAD_FROM_TABLE_V2", 1728, "Cannot load from %s.%s. The table is probably corrupted" },
{ "ER_MASTER_DELAY_VALUE_OUT_OF_RANGE", 1729, "The requested value %s for the master delay exceeds the maximum %u" },
{ "ER_ONLY_FD_AND_RBR_EVENTS_ALLOWED_IN_BINLOG_STATEMENT", 1730, "Only Format_description_log_event and row events are allowed in BINLOG statements (but %s was provided)" },
{ "ER_PARTITION_EXCHANGE_DIFFERENT_OPTION", 1731, "Non matching attribute \'%-.64s\' between partition and table" },
{ "ER_PARTITION_EXCHANGE_PART_TABLE", 1732, "Table to exchange with partition is partitioned: \'%-.64s\'" },
{ "ER_PARTITION_EXCHANGE_TEMP_TABLE", 1733, "Table to exchange with partition is temporary: \'%-.64s\'" },
{ "ER_PARTITION_INSTEAD_OF_SUBPARTITION", 1734, "Subpartitioned table, use subpartition instead of partition" },
{ "ER_UNKNOWN_PARTITION", 1735, "Unknown partition \'%-.64s\' in table \'%-.64s\'" },
{ "ER_TABLES_DIFFERENT_METADATA", 1736, "Tables have different definitions" },
{ "ER_ROW_DOES_NOT_MATCH_PARTITION", 1737, "Found a row that does not match the partition" },
{ "ER_BINLOG_CACHE_SIZE_GREATER_THAN_MAX", 1738, "Option binlog_cache_size (%lu) is greater than max_binlog_cache_size (%lu); setting binlog_cache_size equal to max_binlog_cache_size." },
{ "ER_WARN_INDEX_NOT_APPLICABLE", 1739, "Cannot use %-.64s access on index \'%-.64s\' due to type or collation conversion on field \'%-.64s\'" },
{ "ER_PARTITION_EXCHANGE_FOREIGN_KEY", 1740, "Table to exchange with partition has foreign key references: \'%-.64s\'" },
{ "ER_NO_SUCH_KEY_VALUE", 1741, "Key value \'%-.192s\' was not found in table \'%-.192s.%-.192s\'" },
{ "ER_RPL_INFO_DATA_TOO_LONG", 1742, "Data for column \'%s\' too long" },
{ "ER_NETWORK_READ_EVENT_CHECKSUM_FAILURE", 1743, "Replication event checksum verification failed while reading from network." },
{ "ER_BINLOG_READ_EVENT_CHECKSUM_FAILURE", 1744, "Replication event checksum verification failed while reading from a log file." },
{ "ER_BINLOG_STMT_CACHE_SIZE_GREATER_THAN_MAX", 1745, "Option binlog_stmt_cache_size (%lu) is greater than max_binlog_stmt_cache_size (%lu); setting binlog_stmt_cache_size equal to max_binlog_stmt_cache_size." },
{ "ER_CANT_UPDATE_TABLE_IN_CREATE_TABLE_SELECT", 1746, "Can\'t update table \'%-.192s\' while \'%-.192s\' is being created." },
{ "ER_PARTITION_CLAUSE_ON_NONPARTITIONED", 1747, "PARTITION () clause on non partitioned table" },
{ "ER_ROW_DOES_NOT_MATCH_GIVEN_PARTITION_SET", 1748, "Found a row not matching the given partition set" },
{ "ER_NO_SUCH_PARTITION__UNUSED", 1749, "partition \'%-.64s\' doesn\'t exist" },
{ "ER_CHANGE_RPL_INFO_REPOSITORY_FAILURE", 1750, "Failure while changing the type of replication repository: %s." },
{ "ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_CREATED_TEMP_TABLE", 1751, "The creation of some temporary tables could not be rolled back." },
{ "ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_DROPPED_TEMP_TABLE", 1752, "Some temporary tables were dropped, but these operations could not be rolled back." },
{ "ER_MTS_FEATURE_IS_NOT_SUPPORTED", 1753, "%s is not supported in multi-threaded slave mode. %s" },
{ "ER_MTS_UPDATED_DBS_GREATER_MAX", 1754, "The number of modified databases exceeds the maximum %d; the database names will not be included in the replication event metadata." },
{ "ER_MTS_CANT_PARALLEL", 1755, "Cannot execute the current event group in the parallel mode. Encountered event %s, relay-log name %s, position %s which prevents execution of this event group in parallel mode. Reason: %s." },
{ "ER_MTS_INCONSISTENT_DATA", 1756, "%s" },
{ "ER_FULLTEXT_NOT_SUPPORTED_WITH_PARTITIONING", 1757, "FULLTEXT index is not supported for partitioned tables." },
{ "ER_DA_INVALID_CONDITION_NUMBER", 1758, "Invalid condition number" },
{ "ER_INSECURE_PLAIN_TEXT", 1759, "Sending passwords in plain text without SSL/TLS is extremely insecure." },
{ "ER_INSECURE_CHANGE_MASTER", 1760, "Storing MySQL user name or password information in the master info repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START SLAVE; see the \'START SLAVE Syntax\' in the MySQL Manual for more information." },
{ "ER_FOREIGN_DUPLICATE_KEY_WITH_CHILD_INFO", 1761, "Foreign key constraint for table \'%.192s\', record \'%-.192s\' would lead to a duplicate entry in table \'%.192s\', key \'%.192s\'" },
{ "ER_FOREIGN_DUPLICATE_KEY_WITHOUT_CHILD_INFO", 1762, "Foreign key constraint for table \'%.192s\', record \'%-.192s\' would lead to a duplicate entry in a child table" },
{ "ER_SQLTHREAD_WITH_SECURE_SLAVE", 1763, "Setting authentication options is not possible when only the Slave SQL Thread is being started." },
{ "ER_TABLE_HAS_NO_FT", 1764, "The table does not have FULLTEXT index to support this query" },
{ "ER_VARIABLE_NOT_SETTABLE_IN_SF_OR_TRIGGER", 1765, "The system variable %.200s cannot be set in stored functions or triggers." },
{ "ER_VARIABLE_NOT_SETTABLE_IN_TRANSACTION", 1766, "The system variable %.200s cannot be set when there is an ongoing transaction." },
{ "ER_GTID_NEXT_IS_NOT_IN_GTID_NEXT_LIST", 1767, "The system variable @@SESSION.GTID_NEXT has the value %.200s, which is not listed in @@SESSION.GTID_NEXT_LIST." },
{ "ER_CANT_CHANGE_GTID_NEXT_IN_TRANSACTION", 1768, "The system variable @@SESSION.GTID_NEXT cannot change inside a transaction." },
{ "ER_SET_STATEMENT_CANNOT_INVOKE_FUNCTION", 1769, "The statement \'SET %.200s\' cannot invoke a stored function." },
{ "ER_GTID_NEXT_CANT_BE_AUTOMATIC_IF_GTID_NEXT_LIST_IS_NON_NULL", 1770, "The system variable @@SESSION.GTID_NEXT cannot be \'AUTOMATIC\' when @@SESSION.GTID_NEXT_LIST is non-NULL." },
{ "ER_SKIPPING_LOGGED_TRANSACTION", 1771, "Skipping transaction %.200s because it has already been executed and logged." },
{ "ER_MALFORMED_GTID_SET_SPECIFICATION", 1772, "Malformed GTID set specification \'%.200s\'." },
{ "ER_MALFORMED_GTID_SET_ENCODING", 1773, "Malformed GTID set encoding." },
{ "ER_MALFORMED_GTID_SPECIFICATION", 1774, "Malformed GTID specification \'%.200s\'." },
{ "ER_GNO_EXHAUSTED", 1775, "Impossible to generate Global Transaction Identifier: the integer component reached the maximal value. Restart the server with a new server_uuid." },
{ "ER_BAD_SLAVE_AUTO_POSITION", 1776, "Parameters MASTER_LOG_FILE, MASTER_LOG_POS, RELAY_LOG_FILE and RELAY_LOG_POS cannot be set when MASTER_AUTO_POSITION is active." },
{ "ER_AUTO_POSITION_REQUIRES_GTID_MODE_NOT_OFF", 1777, "CHANGE MASTER TO MASTER_AUTO_POSITION = 1 cannot be executed because @@GLOBAL.GTID_MODE = OFF." },
{ "ER_CANT_DO_IMPLICIT_COMMIT_IN_TRX_WHEN_GTID_NEXT_IS_SET", 1778, "Cannot execute statements with implicit commit inside a transaction when @@SESSION.GTID_NEXT == \'UUID:NUMBER\'." },
{ "ER_GTID_MODE_ON_REQUIRES_ENFORCE_GTID_CONSISTENCY_ON", 1779, "GTID_MODE = ON requires ENFORCE_GTID_CONSISTENCY = ON." },
{ "ER_GTID_MODE_REQUIRES_BINLOG", 1780, "@@GLOBAL.GTID_MODE = ON or ON_PERMISSIVE or OFF_PERMISSIVE requires --log-bin and --log-slave-updates." },
{ "ER_CANT_SET_GTID_NEXT_TO_GTID_WHEN_GTID_MODE_IS_OFF", 1781, "@@SESSION.GTID_NEXT cannot be set to UUID:NUMBER when @@GLOBAL.GTID_MODE = OFF." },
{ "ER_CANT_SET_GTID_NEXT_TO_ANONYMOUS_WHEN_GTID_MODE_IS_ON", 1782, "@@SESSION.GTID_NEXT cannot be set to ANONYMOUS when @@GLOBAL.GTID_MODE = ON." },
{ "ER_CANT_SET_GTID_NEXT_LIST_TO_NON_NULL_WHEN_GTID_MODE_IS_OFF", 1783, "@@SESSION.GTID_NEXT_LIST cannot be set to a non-NULL value when @@GLOBAL.GTID_MODE = OFF." },
{ "ER_FOUND_GTID_EVENT_WHEN_GTID_MODE_IS_OFF__UNUSED", 1784, "Found a Gtid_log_event when @@GLOBAL.GTID_MODE = OFF." },
{ "ER_GTID_UNSAFE_NON_TRANSACTIONAL_TABLE", 1785, "Statement violates GTID consistency: Updates to non-transactional tables can only be done in either autocommitted statements or single-statement transactions, and never in the same statement as updates to transactional tables." },
{ "ER_GTID_UNSAFE_CREATE_SELECT", 1786, "Statement violates GTID consistency: CREATE TABLE ... SELECT." },
{ "ER_GTID_UNSAFE_CREATE_DROP_TEMPORARY_TABLE_IN_TRANSACTION", 1787, "Statement violates GTID consistency: CREATE TEMPORARY TABLE and DROP TEMPORARY TABLE can only be executed outside transactional context.  These statements are also not allowed in a function or trigger because functions and triggers are also considered to be multi-statement transactions." },
{ "ER_GTID_MODE_CAN_ONLY_CHANGE_ONE_STEP_AT_A_TIME", 1788, "The value of @@GLOBAL.GTID_MODE can only be changed one step at a time: OFF <-> OFF_PERMISSIVE <-> ON_PERMISSIVE <-> ON. Also note that this value must be stepped up or down simultaneously on all servers. See the Manual for instructions." },
{ "ER_MASTER_HAS_PURGED_REQUIRED_GTIDS", 1789, "The slave is connecting using CHANGE MASTER TO MASTER_AUTO_POSITION = 1, but the master has purged binary logs containing GTIDs that the slave requires." },
{ "ER_CANT_SET_GTID_NEXT_WHEN_OWNING_GTID", 1790, "@@SESSION.GTID_NEXT cannot be changed by a client that owns a GTID. The client owns %s. Ownership is released on COMMIT or ROLLBACK." },
{ "ER_UNKNOWN_EXPLAIN_FORMAT", 1791, "Unknown EXPLAIN format name: \'%s\'" },
{ "ER_CANT_EXECUTE_IN_READ_ONLY_TRANSACTION", 1792, "Cannot execute statement in a READ ONLY transaction." },
{ "ER_TOO_LONG_TABLE_PARTITION_COMMENT", 1793, "Comment for table partition \'%-.64s\' is too long (max = %lu)" },
{ "ER_SLAVE_CONFIGURATION", 1794, "Slave is not configured or failed to initialize properly. You must at least set --server-id to enable either a master or a slave. Additional error messages can be found in the MySQL error log." },
{ "ER_INNODB_FT_LIMIT", 1795, "InnoDB presently supports one FULLTEXT index creation at a time" },
{ "ER_INNODB_NO_FT_TEMP_TABLE", 1796, "Cannot create FULLTEXT index on temporary InnoDB table" },
{ "ER_INNODB_FT_WRONG_DOCID_COLUMN", 1797, "Column \'%-.192s\' is of wrong type for an InnoDB FULLTEXT index" },
{ "ER_INNODB_FT_WRONG_DOCID_INDEX", 1798, "Index \'%-.192s\' is of wrong type for an InnoDB FULLTEXT index" },
{ "ER_INNODB_ONLINE_LOG_TOO_BIG", 1799, "Creating index \'%-.192s\' required more than \'innodb_online_alter_log_max_size\' bytes of modification log. Please try again." },
{ "ER_UNKNOWN_ALTER_ALGORITHM", 1800, "Unknown ALGORITHM \'%s\'" },
{ "ER_UNKNOWN_ALTER_LOCK", 1801, "Unknown LOCK type \'%s\'" },
{ "ER_MTS_CHANGE_MASTER_CANT_RUN_WITH_GAPS", 1802, "CHANGE MASTER cannot be executed when the slave was stopped with an error or killed in MTS mode. Consider using RESET SLAVE or START SLAVE UNTIL." },
{ "ER_MTS_RECOVERY_FAILURE", 1803, "Cannot recover after SLAVE errored out in parallel execution mode. Additional error messages can be found in the MySQL error log." },
{ "ER_MTS_RESET_WORKERS", 1804, "Cannot clean up worker info tables. Additional error messages can be found in the MySQL error log." },
{ "ER_COL_COUNT_DOESNT_MATCH_CORRUPTED_V2", 1805, "Column count of %s.%s is wrong. Expected %d, found %d. The table is probably corrupted" },
{ "ER_SLAVE_SILENT_RETRY_TRANSACTION", 1806, "Slave must silently retry current transaction" },
{ "ER_DISCARD_FK_CHECKS_RUNNING", 1807, "There is a foreign key check running on table \'%-.192s\'. Cannot discard the table." },
{ "ER_TABLE_SCHEMA_MISMATCH", 1808, "Schema mismatch (%s)" },
{ "ER_TABLE_IN_SYSTEM_TABLESPACE", 1809, "Table \'%-.192s\' in system tablespace" },
{ "ER_IO_READ_ERROR", 1810, "IO Read error: (%lu, %s) %s" },
{ "ER_IO_WRITE_ERROR", 1811, "IO Write error: (%lu, %s) %s" },
{ "ER_TABLESPACE_MISSING", 1812, "Tablespace is missing for table %s." },
{ "ER_TABLESPACE_EXISTS", 1813, "Tablespace \'%-.192s\' exists." },
{ "ER_TABLESPACE_DISCARDED", 1814, "Tablespace has been discarded for table \'%-.192s\'" },
{ "ER_INTERNAL_ERROR", 1815, "Internal error: %s" },
{ "ER_INNODB_IMPORT_ERROR", 1816, "ALTER TABLE %-.192s IMPORT TABLESPACE failed with error %lu : \'%s\'" },
{ "ER_INNODB_INDEX_CORRUPT", 1817, "Index corrupt: %s" },
{ "ER_INVALID_YEAR_COLUMN_LENGTH", 1818, "Supports only YEAR or YEAR(4) column." },
{ "ER_NOT_VALID_PASSWORD", 1819, "Your password does not satisfy the current policy requirements" },
{ "ER_MUST_CHANGE_PASSWORD", 1820, "You must reset your password using ALTER USER statement before executing this statement." },
{ "ER_FK_NO_INDEX_CHILD", 1821, "Failed to add the foreign key constaint. Missing index for constraint \'%s\' in the foreign table \'%s\'" },
{ "ER_FK_NO_INDEX_PARENT", 1822, "Failed to add the foreign key constaint. Missing index for constraint \'%s\' in the referenced table \'%s\'" },
{ "ER_FK_FAIL_ADD_SYSTEM", 1823, "Failed to add the foreign key constraint \'%s\' to system tables" },
{ "ER_FK_CANNOT_OPEN_PARENT", 1824, "Failed to open the referenced table \'%s\'" },
{ "ER_FK_INCORRECT_OPTION", 1825, "Failed to add the foreign key constraint on table \'%s\'. Incorrect options in FOREIGN KEY constraint \'%s\'" },
{ "ER_FK_DUP_NAME", 1826, "Duplicate foreign key constraint name \'%s\'" },
{ "ER_PASSWORD_FORMAT", 1827, "The password hash doesn\'t have the expected format. Check if the correct password algorithm is being used with the PASSWORD() function." },
{ "ER_FK_COLUMN_CANNOT_DROP", 1828, "Cannot drop column \'%-.192s\': needed in a foreign key constraint \'%-.192s\'" },
{ "ER_FK_COLUMN_CANNOT_DROP_CHILD", 1829, "Cannot drop column \'%-.192s\': needed in a foreign key constraint \'%-.192s\' of table \'%-.192s\'" },
{ "ER_FK_COLUMN_NOT_NULL", 1830, "Column \'%-.192s\' cannot be NOT NULL: needed in a foreign key constraint \'%-.192s\' SET NULL" },
{ "ER_DUP_INDEX", 1831, "Duplicate index \'%-.64s\' defined on the table \'%-.64s.%-.64s\'. This is deprecated and will be disallowed in a future release." },
{ "ER_FK_COLUMN_CANNOT_CHANGE", 1832, "Cannot change column \'%-.192s\': used in a foreign key constraint \'%-.192s\'" },
{ "ER_FK_COLUMN_CANNOT_CHANGE_CHILD", 1833, "Cannot change column \'%-.192s\': used in a foreign key constraint \'%-.192s\' of table \'%-.192s\'" },
{ "ER_UNUSED5", 1834, "Cannot delete rows from table which is parent in a foreign key constraint \'%-.192s\' of table \'%-.192s\'" },
{ "ER_MALFORMED_PACKET", 1835, "Malformed communication packet." },
{ "ER_READ_ONLY_MODE", 1836, "Running in read-only mode" },
{ "ER_GTID_NEXT_TYPE_UNDEFINED_GROUP", 1837, "When @@SESSION.GTID_NEXT is set to a GTID, you must explicitly set it to a different value after a COMMIT or ROLLBACK. Please check GTID_NEXT variable manual page for detailed explanation. Current @@SESSION.GTID_NEXT is \'%s\'." },
{ "ER_VARIABLE_NOT_SETTABLE_IN_SP", 1838, "The system variable %.200s cannot be set in stored procedures." },
{ "ER_CANT_SET_GTID_PURGED_WHEN_GTID_MODE_IS_OFF", 1839, "@@GLOBAL.GTID_PURGED can only be set when @@GLOBAL.GTID_MODE = ON." },
{ "ER_CANT_SET_GTID_PURGED_WHEN_GTID_EXECUTED_IS_NOT_EMPTY", 1840, "@@GLOBAL.GTID_PURGED can only be set when @@GLOBAL.GTID_EXECUTED is empty." },
{ "ER_CANT_SET_GTID_PURGED_WHEN_OWNED_GTIDS_IS_NOT_EMPTY", 1841, "@@GLOBAL.GTID_PURGED can only be set when there are no ongoing transactions (not even in other clients)." },
{ "ER_GTID_PURGED_WAS_CHANGED", 1842, "@@GLOBAL.GTID_PURGED was changed from \'%s\' to \'%s\'." },
{ "ER_GTID_EXECUTED_WAS_CHANGED", 1843, "@@GLOBAL.GTID_EXECUTED was changed from \'%s\' to \'%s\'." },
{ "ER_BINLOG_STMT_MODE_AND_NO_REPL_TABLES", 1844, "Cannot execute statement: impossible to write to binary log since BINLOG_FORMAT = STATEMENT, and both replicated and non replicated tables are written to." },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED", 1845, "%s is not supported for this operation. Try %s." },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON", 1846, "%s is not supported. Reason: %s. Try %s." },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COPY", 1847, "COPY algorithm requires a lock" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_PARTITION", 1848, "Partition specific operations do not yet support LOCK/ALGORITHM" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_RENAME", 1849, "Columns participating in a foreign key are renamed" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COLUMN_TYPE", 1850, "Cannot change column type INPLACE" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_CHECK", 1851, "Adding foreign keys needs foreign_key_checks=OFF" },
{ "ER_UNUSED6", 1852, "Creating unique indexes with IGNORE requires COPY algorithm to remove duplicate rows" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOPK", 1853, "Dropping a primary key is not allowed without also adding a new primary key" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_AUTOINC", 1854, "Adding an auto-increment column requires a lock" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_HIDDEN_FTS", 1855, "Cannot replace hidden FTS_DOC_ID with a user-visible one" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_CHANGE_FTS", 1856, "Cannot drop or rename FTS_DOC_ID" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FTS", 1857, "Fulltext index creation requires a lock" },
{ "ER_SQL_SLAVE_SKIP_COUNTER_NOT_SETTABLE_IN_GTID_MODE", 1858, "sql_slave_skip_counter can not be set when the server is running with @@GLOBAL.GTID_MODE = ON. Instead, for each transaction that you want to skip, generate an empty transaction with the same GTID as the transaction" },
{ "ER_DUP_UNKNOWN_IN_INDEX", 1859, "Duplicate entry for key \'%-.192s\'" },
{ "ER_IDENT_CAUSES_TOO_LONG_PATH", 1860, "Long database name and identifier for object resulted in path length exceeding %d characters. Path: \'%s\'." },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOT_NULL", 1861, "cannot silently convert NULL values, as required in this SQL_MODE" },
{ "ER_MUST_CHANGE_PASSWORD_LOGIN", 1862, "Your password has expired. To log in you must change it using a client that supports expired passwords." },
{ "ER_ROW_IN_WRONG_PARTITION", 1863, "Found a row in wrong partition %s" },
{ "ER_MTS_EVENT_BIGGER_PENDING_JOBS_SIZE_MAX", 1864, "Cannot schedule event %s, relay-log name %s, position %s to Worker thread because its size %lu exceeds %lu of slave_pending_jobs_size_max." },
{ "ER_INNODB_NO_FT_USES_PARSER", 1865, "Cannot CREATE FULLTEXT INDEX WITH PARSER on InnoDB table" },
{ "ER_BINLOG_LOGICAL_CORRUPTION", 1866, "The binary log file \'%s\' is logically corrupted: %s" },
{ "ER_WARN_PURGE_LOG_IN_USE", 1867, "file %s was not purged because it was being read by %d thread(s), purged only %d out of %d files." },
{ "ER_WARN_PURGE_LOG_IS_ACTIVE", 1868, "file %s was not purged because it is the active log file." },
{ "ER_AUTO_INCREMENT_CONFLICT", 1869, "Auto-increment value in UPDATE conflicts with internally generated values" },
{ "WARN_ON_BLOCKHOLE_IN_RBR", 1870, "Row events are not logged for %s statements that modify BLACKHOLE tables in row format. Table(s): \'%-.192s\'" },
{ "ER_SLAVE_MI_INIT_REPOSITORY", 1871, "Slave failed to initialize master info structure from the repository" },
{ "ER_SLAVE_RLI_INIT_REPOSITORY", 1872, "Slave failed to initialize relay log info structure from the repository" },
{ "ER_ACCESS_DENIED_CHANGE_USER_ERROR", 1873, "Access denied trying to change to user \'%-.48s\'@\'%-.64s\' (using password: %s). Disconnecting." },
{ "ER_INNODB_READ_ONLY", 1874, "InnoDB is in read only mode." },
{ "ER_STOP_SLAVE_SQL_THREAD_TIMEOUT", 1875, "STOP SLAVE command execution is incomplete: Slave SQL thread got the stop signal, thread is busy, SQL thread will stop once the current task is complete." },
{ "ER_STOP_SLAVE_IO_THREAD_TIMEOUT", 1876, "STOP SLAVE command execution is incomplete: Slave IO thread got the stop signal, thread is busy, IO thread will stop once the current task is complete." },
{ "ER_TABLE_CORRUPT", 1877, "Operation cannot be performed. The table \'%-.64s.%-.64s\' is missing, corrupt or contains bad data." },
{ "ER_TEMP_FILE_WRITE_FAILURE", 1878, "Temporary file write failure." },
{ "ER_INNODB_FT_AUX_NOT_HEX_ID", 1879, "Upgrade index name failed, please use create index(alter table) algorithm copy to rebuild index." },
{ "ER_OLD_TEMPORALS_UPGRADED", 1880, "TIME/TIMESTAMP/DATETIME columns of old format have been upgraded to the new format." },
{ "ER_INNODB_FORCED_RECOVERY", 1881, "Operation not allowed when innodb_forced_recovery > 0." },
{ "ER_AES_INVALID_IV", 1882, "The initialization vector supplied to %s is too short. Must be at least %d bytes long" },
{ "ER_PLUGIN_CANNOT_BE_UNINSTALLED", 1883, "Plugin \'%s\' cannot be uninstalled now. %s" },
{ "ER_GTID_UNSAFE_BINLOG_SPLITTABLE_STATEMENT_AND_GTID_GROUP", 1884, "Cannot execute statement because it needs to be written to the binary log as multiple statements, and this is not allowed when @@SESSION.GTID_NEXT == \'UUID:NUMBER\'." },
{ "ER_SLAVE_HAS_MORE_GTIDS_THAN_MASTER", 1885, "Slave has more GTIDs than the master has, using the master\'s SERVER_UUID. This may indicate that the end of the binary log was truncated or that the last binary log file was lost, e.g., after a power or disk failure when sync_binlog != 1. The master may or may not have rolled back transactions that were already replicated to the slave. Suggest to replicate any transactions that master has rolled back from slave to master, and/or commit empty transactions on master to account for transactions that have been committed on master but are not included in GTID_EXECUTED." },
{ "ER_FILE_CORRUPT", 3000, "File %s is corrupted" },
{ "ER_ERROR_ON_MASTER", 3001, "Query partially completed on the master (error on master: %d) and was aborted. There is a chance that your master is inconsistent at this point. If you are sure that your master is ok, run this query manually on the slave and then restart the slave with SET GLOBAL SQL_SLAVE_SKIP_COUNTER=1; START SLAVE;. Query:\'%s\'" },
{ "ER_INCONSISTENT_ERROR", 3002, "Query caused different errors on master and slave. Error on master: message (format)=\'%s\' error code=%d; Error on slave:actual message=\'%s\', error code=%d. Default database:\'%s\'. Query:\'%s\'" },
{ "ER_STORAGE_ENGINE_NOT_LOADED", 3003, "Storage engine for table \'%s\'.\'%s\' is not loaded." },
{ "ER_GET_STACKED_DA_WITHOUT_ACTIVE_HANDLER", 3004, "GET STACKED DIAGNOSTICS when handler not active" },
{ "ER_WARN_LEGACY_SYNTAX_CONVERTED", 3005, "%s is no longer supported. The statement was converted to %s." },
{ "ER_BINLOG_UNSAFE_FULLTEXT_PLUGIN", 3006, "Statement is unsafe because it uses a fulltext parser plugin which may not return the same value on the slave." },
{ "ER_CANNOT_DISCARD_TEMPORARY_TABLE", 3007, "Cannot DISCARD/IMPORT tablespace associated with temporary table" },
{ "ER_FK_DEPTH_EXCEEDED", 3008, "Foreign key cascade delete/update exceeds max depth of %d." },
{ "ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE_V2", 3009, "Column count of %s.%s is wrong. Expected %d, found %d. Created with MySQL %d, now running %d. Please use mysql_upgrade to fix this error." },
{ "ER_WARN_TRIGGER_DOESNT_HAVE_CREATED", 3010, "Trigger %s.%s.%s does not have CREATED attribute." },
{ "ER_REFERENCED_TRG_DOES_NOT_EXIST", 3011, "Referenced trigger \'%s\' for the given action time and event type does not exist." },
{ "ER_EXPLAIN_NOT_SUPPORTED", 3012, "EXPLAIN FOR CONNECTION command is supported only for SELECT/UPDATE/INSERT/DELETE/REPLACE" },
{ "ER_INVALID_FIELD_SIZE", 3013, "Invalid size for column \'%-.192s\'." },
{ "ER_MISSING_HA_CREATE_OPTION", 3014, "Table storage engine \'%-.64s\' found required create option missing" },
{ "ER_ENGINE_OUT_OF_MEMORY", 3015, "Out of memory in storage engine \'%-.64s\'." },
{ "ER_PASSWORD_EXPIRE_ANONYMOUS_USER", 3016, "The password for anonymous user cannot be expired." },
{ "ER_SLAVE_SQL_THREAD_MUST_STOP", 3017, "This operation cannot be performed with a running slave sql thread; run STOP SLAVE SQL_THREAD first" },
{ "ER_NO_FT_MATERIALIZED_SUBQUERY", 3018, "Cannot create FULLTEXT index on materialized subquery" },
{ "ER_INNODB_UNDO_LOG_FULL", 3019, "Undo Log error: %s" },
{ "ER_INVALID_ARGUMENT_FOR_LOGARITHM", 3020, "Invalid argument for logarithm" },
{ "ER_SLAVE_CHANNEL_IO_THREAD_MUST_STOP", 3021, "This operation cannot be performed with a running slave io thread; run STOP SLAVE IO_THREAD FOR CHANNEL \'%s\' first." },
{ "ER_WARN_OPEN_TEMP_TABLES_MUST_BE_ZERO", 3022, "This operation may not be safe when the slave has temporary tables. The tables will be kept open until the server restarts or until the tables are deleted by any replicated DROP statement. Suggest to wait until slave_open_temp_tables = 0." },
{ "ER_WARN_ONLY_MASTER_LOG_FILE_NO_POS", 3023, "CHANGE MASTER TO with a MASTER_LOG_FILE clause but no MASTER_LOG_POS clause may not be safe. The old position value may not be valid for the new binary log file." },
{ "ER_QUERY_TIMEOUT", 3024, "Query execution was interrupted, maximum statement execution time exceeded" },
{ "ER_NON_RO_SELECT_DISABLE_TIMER", 3025, "Select is not a read only statement, disabling timer" },
{ "ER_DUP_LIST_ENTRY", 3026, "Duplicate entry \'%-.192s\'." },
{ "ER_SQL_MODE_NO_EFFECT", 3027, "\'%s\' mode no longer has any effect. Use STRICT_ALL_TABLES or STRICT_TRANS_TABLES instead." },
{ "ER_AGGREGATE_ORDER_FOR_UNION", 3028, "Expression #%u of ORDER BY contains aggregate function and applies to a UNION" },
{ "ER_AGGREGATE_ORDER_NON_AGG_QUERY", 3029, "Expression #%u of ORDER BY contains aggregate function and applies to the result of a non-aggregated query" },
{ "ER_SLAVE_WORKER_STOPPED_PREVIOUS_THD_ERROR", 3030, "Slave worker has stopped after at least one previous worker encountered an error when slave-preserve-commit-order was enabled. To preserve commit order, the last transaction executed by this thread has not been committed. When restarting the slave after fixing any failed threads, you should fix this worker as well." },
{ "ER_DONT_SUPPORT_SLAVE_PRESERVE_COMMIT_ORDER", 3031, "slave_preserve_commit_order is not supported %s." },
{ "ER_SERVER_OFFLINE_MODE", 3032, "The server is currently in offline mode" },
{ "ER_GIS_DIFFERENT_SRIDS", 3033, "Binary geometry function %s given two geometries of different srids: %u and %u, which should have been identical." },
{ "ER_GIS_UNSUPPORTED_ARGUMENT", 3034, "Calling geometry function %s with unsupported types of arguments." },
{ "ER_GIS_UNKNOWN_ERROR", 3035, "Unknown GIS error occured in function %s." },
{ "ER_GIS_UNKNOWN_EXCEPTION", 3036, "Unknown exception caught in GIS function %s." },
{ "ER_GIS_INVALID_DATA", 3037, "Invalid GIS data provided to function %s." },
{ "ER_BOOST_GEOMETRY_EMPTY_INPUT_EXCEPTION", 3038, "The geometry has no data in function %s." },
{ "ER_BOOST_GEOMETRY_CENTROID_EXCEPTION", 3039, "Unable to calculate centroid because geometry is empty in function %s." },
{ "ER_BOOST_GEOMETRY_OVERLAY_INVALID_INPUT_EXCEPTION", 3040, "Geometry overlay calculation error: geometry data is invalid in function %s." },
{ "ER_BOOST_GEOMETRY_TURN_INFO_EXCEPTION", 3041, "Geometry turn info calculation error: geometry data is invalid in function %s." },
{ "ER_BOOST_GEOMETRY_SELF_INTERSECTION_POINT_EXCEPTION", 3042, "Analysis procedures of intersection points interrupted unexpectedly in function %s." },
{ "ER_BOOST_GEOMETRY_UNKNOWN_EXCEPTION", 3043, "Unknown exception thrown in function %s." },
{ "ER_STD_BAD_ALLOC_ERROR", 3044, "Memory allocation error: %-.256s in function %s." },
{ "ER_STD_DOMAIN_ERROR", 3045, "Domain error: %-.256s in function %s." },
{ "ER_STD_LENGTH_ERROR", 3046, "Length error: %-.256s in function %s." },
{ "ER_STD_INVALID_ARGUMENT", 3047, "Invalid argument error: %-.256s in function %s." },
{ "ER_STD_OUT_OF_RANGE_ERROR", 3048, "Out of range error: %-.256s in function %s." },
{ "ER_STD_OVERFLOW_ERROR", 3049, "Overflow error error: %-.256s in function %s." },
{ "ER_STD_RANGE_ERROR", 3050, "Range error: %-.256s in function %s." },
{ "ER_STD_UNDERFLOW_ERROR", 3051, "Underflow error: %-.256s in function %s." },
{ "ER_STD_LOGIC_ERROR", 3052, "Logic error: %-.256s in function %s." },
{ "ER_STD_RUNTIME_ERROR", 3053, "Runtime error: %-.256s in function %s." },
{ "ER_STD_UNKNOWN_EXCEPTION", 3054, "Unknown exception: %-.384s in function %s." },
{ "ER_GIS_DATA_WRONG_ENDIANESS", 3055, "Geometry byte string must be little endian." },
{ "ER_CHANGE_MASTER_PASSWORD_LENGTH", 3056, "The password provided for the replication user exceeds the maximum length of 32 characters" },
{ "ER_USER_LOCK_WRONG_NAME", 3057, "Incorrect user-level lock name \'%-.192s\'." },
{ "ER_USER_LOCK_DEADLOCK", 3058, "Deadlock found when trying to get user-level lock; try rolling back transaction/releasing locks and restarting lock acquisition." },
{ "ER_REPLACE_INACCESSIBLE_ROWS", 3059, "REPLACE cannot be executed as it requires deleting rows that are not in the view" },
{ "ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_GIS", 3060, "Do not support online operation on table with GIS index" },
{ "ER_ILLEGAL_USER_VAR", 3061, "User variable name \'%-.100s\' is illegal" },
{ "ER_GTID_MODE_OFF", 3062, "Cannot %s when GTID_MODE = OFF." },
{ "ER_UNSUPPORTED_BY_REPLICATION_THREAD", 3063, "Cannot %s from a replication slave thread." },
{ "ER_INCORRECT_TYPE", 3064, "Incorrect type for argument %s in function %s." },
{ "ER_FIELD_IN_ORDER_NOT_SELECT", 3065, "Expression #%u of ORDER BY clause is not in SELECT list, references column \'%-.192s\' which is not in SELECT list; this is incompatible with %s" },
{ "ER_AGGREGATE_IN_ORDER_NOT_SELECT", 3066, "Expression #%u of ORDER BY clause is not in SELECT list, contains aggregate function; this is incompatible with %s" },
{ "ER_INVALID_RPL_WILD_TABLE_FILTER_PATTERN", 3067, "Supplied filter list contains a value which is not in the required format \'db_pattern.table_pattern\'" },
{ "ER_NET_OK_PACKET_TOO_LARGE", 3068, "OK packet too large" },
{ "ER_INVALID_JSON_DATA", 3069, "Invalid JSON data provided to function %s: %s" },
{ "ER_INVALID_GEOJSON_MISSING_MEMBER", 3070, "Invalid GeoJSON data provided to function %s: Missing required member \'%s\'" },
{ "ER_INVALID_GEOJSON_WRONG_TYPE", 3071, "Invalid GeoJSON data provided to function %s: Member \'%s\' must be of type \'%s\'" },
{ "ER_INVALID_GEOJSON_UNSPECIFIED", 3072, "Invalid GeoJSON data provided to function %s" },
{ "ER_DIMENSION_UNSUPPORTED", 3073, "Unsupported number of coordinate dimensions in function %s: Found %u, expected %u" },
{ "ER_SLAVE_CHANNEL_DOES_NOT_EXIST", 3074, "Slave channel \'%s\' does not exist." },
{ "ER_SLAVE_MULTIPLE_CHANNELS_HOST_PORT", 3075, "A slave channel \'%s\' already exists for the given host and port combination." },
{ "ER_SLAVE_CHANNEL_NAME_INVALID_OR_TOO_LONG", 3076, "Couldn\'t create channel: Channel name is either invalid or too long." },
{ "ER_SLAVE_NEW_CHANNEL_WRONG_REPOSITORY", 3077, "To have multiple channels, repository cannot be of type FILE; Please check the repository configuration and convert them to TABLE." },
{ "ER_SLAVE_CHANNEL_DELETE", 3078, "Cannot delete slave info objects for channel \'%s\'." },
{ "ER_SLAVE_MULTIPLE_CHANNELS_CMD", 3079, "Multiple channels exist on the slave. Please provide channel name as an argument." },
{ "ER_SLAVE_MAX_CHANNELS_EXCEEDED", 3080, "Maximum number of replication channels allowed exceeded." },
{ "ER_SLAVE_CHANNEL_MUST_STOP", 3081, "This operation cannot be performed with running replication threads; run STOP SLAVE FOR CHANNEL \'%s\' first" },
{ "ER_SLAVE_CHANNEL_NOT_RUNNING", 3082, "This operation requires running replication threads; configure slave and run START SLAVE FOR CHANNEL \'%s\'" },
{ "ER_SLAVE_CHANNEL_WAS_RUNNING", 3083, "Replication thread(s) for channel \'%s\' are already runnning." },
{ "ER_SLAVE_CHANNEL_WAS_NOT_RUNNING", 3084, "Replication thread(s) for channel \'%s\' are already stopped." },
{ "ER_SLAVE_CHANNEL_SQL_THREAD_MUST_STOP", 3085, "This operation cannot be performed with a running slave sql thread; run STOP SLAVE SQL_THREAD FOR CHANNEL \'%s\' first." },
{ "ER_SLAVE_CHANNEL_SQL_SKIP_COUNTER", 3086, "When sql_slave_skip_counter > 0, it is not allowed to start more than one SQL thread by using \'START SLAVE [SQL_THREAD]\'. Value of sql_slave_skip_counter can only be used by one SQL thread at a time. Please use \'START SLAVE [SQL_THREAD] FOR CHANNEL\' to start the SQL thread which will use the value of sql_slave_skip_counter." },
{ "ER_WRONG_FIELD_WITH_GROUP_V2", 3087, "Expression #%u of %s is not in GROUP BY clause and contains nonaggregated column \'%-.192s\' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by" },
{ "ER_MIX_OF_GROUP_FUNC_AND_FIELDS_V2", 3088, "In aggregated query without GROUP BY, expression #%u of %s contains nonaggregated column \'%-.192s\'; this is incompatible with sql_mode=only_full_group_by" },
{ "ER_WARN_DEPRECATED_SYSVAR_UPDATE", 3089, "Updating \'%s\' is deprecated. It will be made read-only in a future release." },
{ "ER_WARN_DEPRECATED_SQLMODE", 3090, "Changing sql mode \'%s\' is deprecated. It will be removed in a future release." },
{ "ER_CANNOT_LOG_PARTIAL_DROP_DATABASE_WITH_GTID", 3091, "DROP DATABASE failed; some tables may have been dropped but the database directory remains. The GTID has not been added to GTID_EXECUTED and the statement was not written to the binary log. Fix this as follows: (1) remove all files from the database directory %-.192s; (2) SET GTID_NEXT=\'%-.192s\'; (3) DROP DATABASE `%-.192s`." },
{ "ER_GROUP_REPLICATION_CONFIGURATION", 3092, "The server is not configured properly to be an active member of the group. Please see more details on error log." },
{ "ER_GROUP_REPLICATION_RUNNING", 3093, "The START GROUP_REPLICATION command failed since the group is already running." },
{ "ER_GROUP_REPLICATION_APPLIER_INIT_ERROR", 3094, "The START GROUP_REPLICATION command failed as the applier module failed to start." },
{ "ER_GROUP_REPLICATION_STOP_APPLIER_THREAD_TIMEOUT", 3095, "The STOP GROUP_REPLICATION command execution is incomplete: The applier thread got the stop signal while it was busy. The applier thread will stop once the current task is complete." },
{ "ER_GROUP_REPLICATION_COMMUNICATION_LAYER_SESSION_ERROR", 3096, "The START GROUP_REPLICATION command failed as there was an error when initializing the group communication layer." },
{ "ER_GROUP_REPLICATION_COMMUNICATION_LAYER_JOIN_ERROR", 3097, "The START GROUP_REPLICATION command failed as there was an error when joining the communication group." },
{ "ER_BEFORE_DML_VALIDATION_ERROR", 3098, "The table does not comply with the requirements by an external plugin." },
{ "ER_PREVENTS_VARIABLE_WITHOUT_RBR", 3099, "Cannot change the value of variable %s without binary log format as ROW." },
{ "ER_RUN_HOOK_ERROR", 3100, "Error on observer while running replication hook \'%s\'." },
{ "ER_TRANSACTION_ROLLBACK_DURING_COMMIT", 3101, "Plugin instructed the server to rollback the current transaction." },
{ "ER_GENERATED_COLUMN_FUNCTION_IS_NOT_ALLOWED", 3102, "Expression of generated column \'%s\' contains a disallowed function." },
{ "ER_UNSUPPORTED_ALTER_INPLACE_ON_VIRTUAL_COLUMN", 3103, "INPLACE ADD or DROP of virtual columns cannot be combined with other ALTER TABLE actions" },
{ "ER_WRONG_FK_OPTION_FOR_GENERATED_COLUMN", 3104, "Cannot define foreign key with %s clause on a generated column." },
{ "ER_NON_DEFAULT_VALUE_FOR_GENERATED_COLUMN", 3105, "The value specified for generated column \'%s\' in table \'%s\' is not allowed." },
{ "ER_UNSUPPORTED_ACTION_ON_GENERATED_COLUMN", 3106, "\'%s\' is not supported for generated columns." },
{ "ER_GENERATED_COLUMN_NON_PRIOR", 3107, "Generated column can refer only to generated columns defined prior to it." },
{ "ER_DEPENDENT_BY_GENERATED_COLUMN", 3108, "Column \'%s\' has a generated column dependency." },
{ "ER_GENERATED_COLUMN_REF_AUTO_INC", 3109, "Generated column \'%s\' cannot refer to auto-increment column." },
{ "ER_FEATURE_NOT_AVAILABLE", 3110, "The \'%-.64s\' feature is not available; you need to remove \'%-.64s\' or use MySQL built with \'%-.64s\'" },
{ "ER_CANT_SET_GTID_MODE", 3111, "SET @@GLOBAL.GTID_MODE = %-.64s is not allowed because %-.384s." },
{ "ER_CANT_USE_AUTO_POSITION_WITH_GTID_MODE_OFF", 3112, "The replication receiver thread%-.192s cannot start in AUTO_POSITION mode: this server uses @@GLOBAL.GTID_MODE = OFF." },
{ "ER_CANT_REPLICATE_ANONYMOUS_WITH_AUTO_POSITION", 3113, "Cannot replicate anonymous transaction when AUTO_POSITION = 1, at file %.512s, position %lld." },
{ "ER_CANT_REPLICATE_ANONYMOUS_WITH_GTID_MODE_ON", 3114, "Cannot replicate anonymous transaction when @@GLOBAL.GTID_MODE = ON, at file %.512s, position %lld." },
{ "ER_CANT_REPLICATE_GTID_WITH_GTID_MODE_OFF", 3115, "Cannot replicate GTID-transaction when @@GLOBAL.GTID_MODE = OFF, at file %.512s, position %lld." },
{ "ER_CANT_SET_ENFORCE_GTID_CONSISTENCY_ON_WITH_ONGOING_GTID_VIOLATING_TRANSACTIONS", 3116, "Cannot set ENFORCE_GTID_CONSISTENCY = ON because there are ongoing transactions that violate GTID consistency." },
{ "ER_SET_ENFORCE_GTID_CONSISTENCY_WARN_WITH_ONGOING_GTID_VIOLATING_TRANSACTIONS", 3117, "There are ongoing transactions that violate GTID consistency." },
{ "ER_ACCOUNT_HAS_BEEN_LOCKED", 3118, "Access denied for user \'%-.48s\'@\'%-.64s\'. Account is locked." },
{ "ER_WRONG_TABLESPACE_NAME", 3119, "Incorrect tablespace name `%-.192s`" },
{ "ER_TABLESPACE_IS_NOT_EMPTY", 3120, "Tablespace `%-.192s` is not empty." },
{ "ER_WRONG_FILE_NAME", 3121, "Incorrect File Name \'%s\'." },
{ "ER_BOOST_GEOMETRY_INCONSISTENT_TURNS_EXCEPTION", 3122, "Inconsistent intersection points." },
{ "ER_WARN_OPTIMIZER_HINT_SYNTAX_ERROR", 3123, "Optimizer hint syntax error" },
{ "ER_WARN_BAD_MAX_EXECUTION_TIME", 3124, "Unsupported MAX_EXECUTION_TIME" },
{ "ER_WARN_UNSUPPORTED_MAX_EXECUTION_TIME", 3125, "MAX_EXECUTION_TIME hint is supported by top-level standalone SELECT statements only" },
{ "ER_WARN_CONFLICTING_HINT", 3126, "Hint %s is ignored as conflicting/duplicated" },
{ "ER_WARN_UNKNOWN_QB_NAME", 3127, "Query block name %s is not found for %s hint" },
{ "ER_UNRESOLVED_HINT_NAME", 3128, "Unresolved name %s for %s hint" },
{ "ER_WARN_ON_MODIFYING_GTID_EXECUTED_TABLE", 3129, "Please do not modify the %s table. This is a mysql internal system table to store GTIDs for committed transactions. Modifying it can lead to an inconsistent GTID state." },
{ "ER_PLUGGABLE_PROTOCOL_COMMAND_NOT_SUPPORTED", 3130, "Command not supported by pluggable protocols" },
{ "ER_LOCKING_SERVICE_WRONG_NAME", 3131, "Incorrect locking service lock name \'%-.192s\'." },
{ "ER_LOCKING_SERVICE_DEADLOCK", 3132, "Deadlock found when trying to get locking service lock; try releasing locks and restarting lock acquisition." },
{ "ER_LOCKING_SERVICE_TIMEOUT", 3133, "Service lock wait timeout exceeded." },
{ "ER_GIS_MAX_POINTS_IN_GEOMETRY_OVERFLOWED", 3134, "Parameter %s exceeds the maximum number of points in a geometry (%lu) in function %s." },
{ "ER_SQL_MODE_MERGED", 3135, "\'NO_ZERO_DATE\', \'NO_ZERO_IN_DATE\' and \'ERROR_FOR_DIVISION_BY_ZERO\' sql modes should be used with strict mode. They will be merged with strict mode in a future release." },
{ "ER_VTOKEN_PLUGIN_TOKEN_MISMATCH", 3136, "Version token mismatch for %.*s. Correct value %.*s" },
{ "ER_VTOKEN_PLUGIN_TOKEN_NOT_FOUND", 3137, "Version token %.*s not found." },
{ "ER_CANT_SET_VARIABLE_WHEN_OWNING_GTID", 3138, "Variable %-.192s cannot be changed by a client that owns a GTID. The client owns %s. Ownership is released on COMMIT or ROLLBACK." },
{ "ER_SLAVE_CHANNEL_OPERATION_NOT_ALLOWED", 3139, "%-.192s cannot be performed on channel \'%-.192s\'." },
{ "ER_INVALID_JSON_TEXT", 3140, "Invalid JSON text: \"%s\" at position %u in value for column \'%-.200s\'." },
{ "ER_INVALID_JSON_TEXT_IN_PARAM", 3141, "Invalid JSON text in argument %u to function %s: \"%s\" at position %u.%-.0s" },
{ "ER_INVALID_JSON_BINARY_DATA", 3142, "The JSON binary value contains invalid data." },
{ "ER_INVALID_JSON_PATH", 3143, "Invalid JSON path expression. The error is around character position %u.%-.200s" },
{ "ER_INVALID_JSON_CHARSET", 3144, "Cannot create a JSON value from a string with CHARACTER SET \'%s\'." },
{ "ER_INVALID_JSON_CHARSET_IN_FUNCTION", 3145, "Invalid JSON character data provided to function %s: \'%s\'; utf8 is required." },
{ "ER_INVALID_TYPE_FOR_JSON", 3146, "Invalid data type for JSON data in argument %u to function %s; a JSON string or JSON type is required." },
{ "ER_INVALID_CAST_TO_JSON", 3147, "Cannot CAST value to JSON." },
{ "ER_INVALID_JSON_PATH_CHARSET", 3148, "A path expression must be encoded in the utf8 character set. The path expression \'%-.200s\' is encoded in character set \'%-.200s\'." },
{ "ER_INVALID_JSON_PATH_WILDCARD", 3149, "In this situation, path expressions may not contain the * and ** tokens." },
{ "ER_JSON_VALUE_TOO_BIG", 3150, "The JSON value is too big to be stored in a JSON column." },
{ "ER_JSON_KEY_TOO_BIG", 3151, "The JSON object contains a key name that is too long." },
{ "ER_JSON_USED_AS_KEY", 3152, "JSON column \'%-.192s\' cannot be used in key specification." },
{ "ER_JSON_VACUOUS_PATH", 3153, "The path expression \'$\' is not allowed in this context." },
{ "ER_JSON_BAD_ONE_OR_ALL_ARG", 3154, "The oneOrAll argument to %s may take these values: \'one\' or \'all\'." },
{ "ER_NUMERIC_JSON_VALUE_OUT_OF_RANGE", 3155, "Out of range JSON value for CAST to %s%-.0s from column %s at row %ld" },
{ "ER_INVALID_JSON_VALUE_FOR_CAST", 3156, "Invalid JSON value for CAST to %s%-.0s from column %s at row %ld" },
{ "ER_JSON_DOCUMENT_TOO_DEEP", 3157, "The JSON document exceeds the maximum depth." },
{ "ER_JSON_DOCUMENT_NULL_KEY", 3158, "JSON documents may not contain NULL member names." },
{ "ER_SECURE_TRANSPORT_REQUIRED", 3159, "Connections using insecure transport are prohibited while --require_secure_transport=ON." },
{ "ER_NO_SECURE_TRANSPORTS_CONFIGURED", 3160, "No secure transports (SSL or Shared Memory) are configured, unable to set --require_secure_transport=ON." },
{ "ER_DISABLED_STORAGE_ENGINE", 3161, "Storage engine %s is disabled (Table creation is disallowed)." },
{ "ER_USER_DOES_NOT_EXIST", 3162, "User %s does not exist." },
{ "ER_USER_ALREADY_EXISTS", 3163, "User %s already exists." },
{ "ER_AUDIT_API_ABORT", 3164, "Aborted by Audit API (\'%-.48s\';%d)." },
{ "ER_INVALID_JSON_PATH_ARRAY_CELL", 3165, "A path expression is not a path to a cell in an array." },
{ "ER_BUFPOOL_RESIZE_INPROGRESS", 3166, "Another buffer pool resize is already in progress." },
{ "ER_FEATURE_DISABLED_SEE_DOC", 3167, "The \'%s\' feature is disabled; see the documentation for \'%s\'" },
{ "ER_SERVER_ISNT_AVAILABLE", 3168, "Server isn\'t available" },
{ "ER_SESSION_WAS_KILLED", 3169, "Session was killed" },
{ "ER_CAPACITY_EXCEEDED", 3170, "Memory capacity of %llu bytes for \'%s\' exceeded. %s" },
{ "ER_CAPACITY_EXCEEDED_IN_RANGE_OPTIMIZER", 3171, "Range optimization was not done for this query." },
{ "ER_TABLE_NEEDS_UPG_PART", 3172, "Partitioning upgrade required. Please dump/reload to fix it or do: ALTER TABLE `%-.192s`.`%-.192s` UPGRADE PARTITIONING" },
{ "ER_CANT_WAIT_FOR_EXECUTED_GTID_SET_WHILE_OWNING_A_GTID", 3173, "The client holds ownership of the GTID %s. Therefore, WAIT_FOR_EXECUTED_GTID_SET cannot wait for this GTID." },
{ "ER_CANNOT_ADD_FOREIGN_BASE_COL_VIRTUAL", 3174, "Cannot add foreign key on the base column of indexed virtual column." },
{ "ER_CANNOT_CREATE_VIRTUAL_INDEX_CONSTRAINT", 3175, "Cannot create index on virtual column whose base column has foreign constraint." },
{ "ER_ERROR_ON_MODIFYING_GTID_EXECUTED_TABLE", 3176, "Please do not modify the %s table with an XA transaction. This is an internal system table used to store GTIDs for committed transactions. Although modifying it can lead to an inconsistent GTID state, if neccessary you can modify it with a non-XA transaction." },
{ "ER_LOCK_REFUSED_BY_ENGINE", 3177, "Lock acquisition refused by storage engine." },
{ "ER_UNSUPPORTED_ALTER_ONLINE_ON_VIRTUAL_COLUMN", 3178, "ADD COLUMN col...VIRTUAL, ADD INDEX(col)" },
{ "ER_MASTER_KEY_ROTATION_NOT_SUPPORTED_BY_SE", 3179, "Master key rotation is not supported by storage engine." },
{ "ER_MASTER_KEY_ROTATION_ERROR_BY_SE", 3180, "Encryption key rotation error reported by SE: %s" },
{ "ER_MASTER_KEY_ROTATION_BINLOG_FAILED", 3181, "Write to binlog failed. However, master key rotation has been completed successfully." },
{ "ER_MASTER_KEY_ROTATION_SE_UNAVAILABLE", 3182, "Storage engine is not available." },
{ "ER_TABLESPACE_CANNOT_ENCRYPT", 3183, "This tablespace can\'t be encrypted." },
{ "ER_INVALID_ENCRYPTION_OPTION", 3184, "Invalid encryption option." },
{ "ER_CANNOT_FIND_KEY_IN_KEYRING", 3185, "Can\'t find master key from keyring, please check keyring plugin is loaded." },
{ "ER_CAPACITY_EXCEEDED_IN_PARSER", 3186, "Parser bailed out for this query." },
{ "ER_UNSUPPORTED_ALTER_ENCRYPTION_INPLACE", 3187, "Cannot alter encryption attribute by inplace algorithm." },
{ "ER_KEYRING_UDF_KEYRING_SERVICE_ERROR", 3188, "Function \'%s\' failed because underlying keyring service returned an error. Please check if a keyring plugin is installed and that provided arguments are valid for the keyring you are using." },
{ "ER_USER_COLUMN_OLD_LENGTH", 3189, "It seems that your db schema is old. The %s column is 77 characters long and should be 93 characters long. Please run mysql_upgrade." },
{ "ER_CANT_RESET_MASTER", 3190, "RESET MASTER is not allowed because %-.384s." },
{ "ER_GROUP_REPLICATION_MAX_GROUP_SIZE", 3191, "The START GROUP_REPLICATION command failed since the group already has 9 members." },
{ "ER_CANNOT_ADD_FOREIGN_BASE_COL_STORED", 3192, "Cannot add foreign key on the base column of stored column. " },
{ "ER_TABLE_REFERENCED", 3193, "Cannot complete the operation because table is referenced by another connection." },
