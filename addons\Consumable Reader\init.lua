local core_mainmenu = require("core_mainmenu")
local lib_helpers = require("solylib.helpers")
local lib_menu = require("solylib.menu")
local lib_items = require("solylib.items.items")
local cfg = require("Consumable Reader.configuration")
local optionsLoaded, options = pcall(require, "Consumable Reader.options")

local optionsFileName = "addons/Consumable Reader/options.lua"
local ConfigurationWindow

if optionsLoaded then
    -- If options loaded, make sure we have all those we need
    options.configurationEnableWindow = lib_helpers.NotNilOrDefault(options.configurationEnableWindow, true)
    options.EnableWindow = lib_helpers.NotNilOrDefault(options.EnableWindow, true)
    options.Monomate = lib_helpers.NotNilOrDefault(options.Monomate, true)
    options.MonomateName = lib_helpers.NotNilOrDefault(options.MonomateName, " Monomate ")
    options.Dimate = lib_helpers.NotNilOrDefault(options.Dimate, true)
    options.DimateName = lib_helpers.NotNilOrDefault(options.DimateName, " Dimate ")
    options.Trimate = lib_helpers.NotNilOrDefault(options.Trimate, true)
    options.TrimateName = lib_helpers.NotNilOrDefault(options.TrimateName, " Trimate ")
    options.Monofluid = lib_helpers.NotNilOrDefault(options.Monofluid, true)
    options.MonofluidName = lib_helpers.NotNilOrDefault(options.MonofluidName, " Monofluid ")
    options.Difluid = lib_helpers.NotNilOrDefault(options.Difluid, true)
    options.DifluidName = lib_helpers.NotNilOrDefault(options.DifluidName, " Difluid ")
    options.Trifluid = lib_helpers.NotNilOrDefault(options.Trifluid, true)
    options.TrifluidName = lib_helpers.NotNilOrDefault(options.TrifluidName, " Trifluid ")
    options.Sol = lib_helpers.NotNilOrDefault(options.Sol, true)
    options.SolName = lib_helpers.NotNilOrDefault(options.SolName, " Sol Atomizer ")
    options.Moon = lib_helpers.NotNilOrDefault(options.Moon, true)
    options.MoonName = lib_helpers.NotNilOrDefault(options.MoonName, " Moon Atomizer ")
    options.Star = lib_helpers.NotNilOrDefault(options.Star, true)
    options.StarName = lib_helpers.NotNilOrDefault(options.StarName, " Star Atomizer ")
    options.Antidote = lib_helpers.NotNilOrDefault(options.Antidote, true)
    options.AntidoteName = lib_helpers.NotNilOrDefault(options.AntidoteName, " Antidote ")
    options.Antiparalysis = lib_helpers.NotNilOrDefault(options.Antiparalysis, true)
    options.AntiparalysisName = lib_helpers.NotNilOrDefault(options.AntiparalysisName, " Antiparalysis ")
    options.Telepipe = lib_helpers.NotNilOrDefault(options.Telepipe, true)
    options.TelepipeName = lib_helpers.NotNilOrDefault(options.TelepipeName, " Telepipe ")
    options.Trap = lib_helpers.NotNilOrDefault(options.Trap, true)
    options.TrapName = lib_helpers.NotNilOrDefault(options.TrapName, " Trap Vision ")
    options.Scape = lib_helpers.NotNilOrDefault(options.Scape, true)
    options.ScapeName = lib_helpers.NotNilOrDefault(options.ScapeName, " Scape Doll ")
    options.ScapeCountLow = lib_helpers.NotNilOrDefault(options.ScapeCountLow, 0)
    options.ScapeCountHigh = lib_helpers.NotNilOrDefault(options.ScapeCountHigh, 2)
    options.HideWhenMenu = lib_helpers.NotNilOrDefault(options.HideWhenMenu, true)
    options.HideWhenSymbolChat = lib_helpers.NotNilOrDefault(options.HideWhenSymbolChat, true)
    options.HideWhenMenuUnavailable = lib_helpers.NotNilOrDefault(options.HideWhenMenuUnavailable, true)
    options.changed = lib_helpers.NotNilOrDefault(options.changed, true)
    options.Anchor = lib_helpers.NotNilOrDefault(options.Anchor, 1)
    options.X = lib_helpers.NotNilOrDefault(options.X, 50)
    options.Y = lib_helpers.NotNilOrDefault(options.Y, 50)
    options.W = lib_helpers.NotNilOrDefault(options.W, 1000)
    options.H = lib_helpers.NotNilOrDefault(options.H, 60)
    options.NoTitleBar = lib_helpers.NotNilOrDefault(options.NoTitleBar, "")
    options.NoResize = lib_helpers.NotNilOrDefault(options.NoResize, "")
    options.NoMove = lib_helpers.NotNilOrDefault(options.NoMove, "")
    options.TransparentWindow = lib_helpers.NotNilOrDefault(options.TransparentWindow, false)
else
    options =
    {
        configurationEnableWindow = true,
        EnableWindow = true,
        Monomate = true,
        MonomateName = " Monomate ",
        Dimate = true,
        DimateName = " Dimate ",
        Trimate = true,
        TrimateName = " Trimate ",
        Monofluid = true,
        MonofluidName = " Monofluid ",
        Difluid = true,
        DifluidName = " Difluid ",
        Trifluid = true,
        TrifluidName = " Trifluid ",
        Sol = true,
        SolName = " Sol Atomizer ",
        Moon = true,
        MoonName = " Moon Atomizer ",
        Star = true,
        StarName = " Star Atomizer ",
        Antidote = true,
        AntidoteName = " Antidote ",
        Antiparalysis = true,
        AntiparalysisName = " Antiparalysis ",
        Telepipe = true,
        TelepipeName = " Telepipe ",
        Trap = true,
        TrapName = " Trap Vision ",
        Scape = true,
        ScapeName = " Scape Doll ",
        ScapeCountLow = 0,
        ScapeCountHigh = 2,
        HideWhenMenu = false,
        HideWhenSymbolChat = false,
        HideWhenMenuUnavailable = false,
        changed = true,
        Anchor = 1,
        X = 50,
        Y = 50,
        W = 1000,
        H = 60,
        NoTitleBar = "",
        NoResize = "",
        NoMove = "",
        AlwaysAutoResize = "",
        TransparentWindow = false,
    }
end


local function SaveOptions(options)
    local file = io.open(optionsFileName, "w")
    if file ~= nil then
        io.output(file)

        io.write("return\n")
        io.write("{\n")
        io.write(string.format("    configurationEnableWindow = %s,\n", tostring(options.configurationEnableWindow)))
        io.write(string.format("    EnableWindow = %s,\n", tostring(options.EnableWindow)))
        io.write(string.format("    Monomate = %s,\n", tostring(options.Monomate)))
        io.write(string.format("    MonomateName = \"%s\",\n", options.MonomateName))
        io.write(string.format("    Dimate = %s,\n", tostring(options.Dimate)))
        io.write(string.format("    DimateName = \"%s\",\n", options.DimateName))
        io.write(string.format("    Trimate = %s,\n", tostring(options.Trimate)))
        io.write(string.format("    TrimateName = \"%s\",\n", options.TrimateName))
        io.write(string.format("    Monofluid = %s,\n", tostring(options.Monofluid)))
        io.write(string.format("    MonofluidName = \"%s\",\n", options.MonofluidName))
        io.write(string.format("    Difluid = %s,\n", tostring(options.Difluid)))
        io.write(string.format("    DifluidName = \"%s\",\n", options.DifluidName))
        io.write(string.format("    Trifluid = %s,\n", tostring(options.Trifluid)))
        io.write(string.format("    TrifluidName = \"%s\",\n", options.TrifluidName))
        io.write(string.format("    Sol = %s,\n", tostring(options.Sol)))
        io.write(string.format("    SolName = \"%s\",\n", options.SolName))
        io.write(string.format("    Moon = %s,\n", tostring(options.Moon)))
        io.write(string.format("    MoonName = \"%s\",\n", options.MoonName))
        io.write(string.format("    Star = %s,\n", tostring(options.Star)))
        io.write(string.format("    StarName = \"%s\",\n", options.StarName))
        io.write(string.format("    Antidote = %s,\n", tostring(options.Antidote)))
        io.write(string.format("    AntidoteName = \"%s\",\n", options.AntidoteName))
        io.write(string.format("    Antiparalysis = %s,\n", tostring(options.Antiparalysis)))
        io.write(string.format("    AntiparalysisName = \"%s\",\n", options.AntiparalysisName))
        io.write(string.format("    Telepipe = %s,\n", tostring(options.Telepipe)))
        io.write(string.format("    TelepipeName = \"%s\",\n", options.TelepipeName))
        io.write(string.format("    Trap = %s,\n", tostring(options.Trap)))
        io.write(string.format("    TrapName = \"%s\",\n", options.TrapName))
        io.write(string.format("    Scape = %s,\n", tostring(options.Scape)))
        io.write(string.format("    ScapeName = \"%s\",\n", options.ScapeName))
        io.write(string.format("    ScapeCountLow = %i,\n", options.ScapeCountLow))
        io.write(string.format("    ScapeCountHigh = %i,\n", options.ScapeCountHigh))
        io.write(string.format("    HideWhenMenu = %s,\n", tostring(options.HideWhenMenu)))
        io.write(string.format("    HideWhenSymbolChat = %s,\n", tostring(options.HideWhenSymbolChat)))
        io.write(string.format("    HideWhenMenuUnavailable = %s,\n", tostring(options.HideWhenMenuUnavailable)))
        io.write(string.format("    Anchor = %i,\n", options.Anchor))
        io.write(string.format("    X = %i,\n", options.X))
        io.write(string.format("    Y = %i,\n", options.Y))
        io.write(string.format("    W = %i,\n", options.W))
        io.write(string.format("    H = %i,\n", options.H))
        io.write(string.format("    NoTitleBar = \"%s\",\n", options.NoTitleBar))
        io.write(string.format("    NoResize = \"%s\",\n", options.NoResize))
        io.write(string.format("    NoMove = \"%s\",\n", options.NoMove))
        io.write(string.format("    AlwaysAutoResize = \"%s\",\n", options.AlwaysAutoResize))
        io.write(string.format("    TransparentWindow = %s,\n", options.TransparentWindow))
        io.write("}\n")

        io.close(file)
    end
end

local toolColor =
{
    0xFFFF0000,
    0xFFFF3200,
    0xFFFF6400,
    0xFFFF9600,
    0xFFFFC800,
    0xFFE1FF00,
    0xFFAFFF00,
    0xFF7DFF00,
    0xFF4BFF00,
    0xFF19FF00,
    0xFF00FF00,
}

local function PresentScapes()
    local itemList = lib_items.GetItemList(lib_items.Me, false)
    local itemCount = table.getn(itemList)
    local monomate = 0
    local dimate = 0
    local trimate = 0
    local monofluid = 0
    local difluid = 0
    local trifluid = 0
    local sol = 0
    local moon = 0
    local star = 0
    local antidote = 0
    local antiparalysis = 0
    local telepipe = 0
    local trap = 0
    local scape = 0
    for i = 1, itemCount, 1 do
        if itemList[i].hex == 0x030000 then --Monomate
            monomate = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030001 then --Dimate
            dimate = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030002 then --Trimate
            trimate = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030100 then --Monofluid
            monofluid = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030101 then --Difluid
            difluid = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030102 then --Trifluid
            trifluid = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030300 then --Sol Atomizer
            sol = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030400 then --Moon Atomizer
            moon = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030500 then --Star Atomizer
            star = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030600 then --Antidote
            antidote = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030601 then --Antiparalysis
            antiparalysis = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030700 then --Telepipe
            telepipe = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030800 then --Trap Vision
            trap = itemList[i].tool.count
        end
        if itemList[i].hex == 0x030900 then --Scape Doll
            scape = scape + 1
        end
    end
    local color = 0xFF00F714
    if scape <= options.ScapeCountLow then
        color = 0xFFFF0000
    elseif scape < options.ScapeCountHigh then
        color = 0xFFFFAA00
    end
    if options.Monomate == true then
        lib_helpers.TextC(false, toolColor[monomate+1], "%i%s", monomate, options.MonomateName)
    end
    if options.Dimate == true then
        lib_helpers.TextC(false, toolColor[dimate+1], "%i%s", dimate, options.DimateName)
    end
    if options.Trimate == true then
        lib_helpers.TextC(false, toolColor[trimate+1], "%i%s", trimate, options.TrimateName)
    end
    if options.Monofluid == true then
        lib_helpers.TextC(false, toolColor[monofluid+1], "%i%s", monofluid, options.MonofluidName)
    end
    if options.Difluid == true then
        lib_helpers.TextC(false, toolColor[difluid+1], "%i%s", difluid, options.DifluidName)
    end
    if options.Trifluid == true then
        lib_helpers.TextC(false, toolColor[trifluid+1], "%i%s", trifluid, options.TrifluidName)
    end
    if options.Sol == true then
        lib_helpers.TextC(false, toolColor[sol+1], "%i%s", sol, options.SolName)
    end
    if options.Moon == true then
        lib_helpers.TextC(false, toolColor[moon+1], "%i%s", moon, options.MoonName)
    end
    if options.Star == true then
        lib_helpers.TextC(false, toolColor[star+1], "%i%s", star, options.StarName)
    end
    if options.Antidote == true then
        lib_helpers.TextC(false, toolColor[antidote+1], "%i%s", antidote, options.AntidoteName)
    end
    if options.Antiparalysis == true then
        lib_helpers.TextC(false, toolColor[antiparalysis+1], "%i%s", antiparalysis, options.AntiparalysisName)
    end
    if options.Telepipe == true then
        lib_helpers.TextC(false, toolColor[telepipe+1], "%i%s", telepipe, options.TelepipeName)
    end
    if options.Trap == true then
        lib_helpers.TextC(false, toolColor[trap+1], "%i%s", trap, options.TrapName)
    end
    if options.Scape == true then
        lib_helpers.TextC(false, color, "%i%s", scape, options.ScapeName)
    end
end


local function present()
    -- If the addon has never been used, open the config window
    -- and disable the config window setting
    if options.configurationEnableWindow then
        ConfigurationWindow.open = true
        options.configurationEnableWindow = false
    end
    ConfigurationWindow.Update()

    if ConfigurationWindow.changed then
        ConfigurationWindow.changed = false
        SaveOptions(options)
    end

    if (options.EnableWindow == true)
            and (options.HideWhenMenu == false or lib_menu.IsMenuOpen() == false)
            and (options.HideWhenSymbolChat == false or lib_menu.IsSymbolChatOpen() == false)
            and (options.HideWhenMenuUnavailable == false or lib_menu.IsMenuUnavailable() == false) then
        local windowName = "Consumable Reader"

        if options.TransparentWindow == true then
            imgui.PushStyleColor("WindowBg", 0.0, 0.0, 0.0, 0.0)
        end


        if options.AlwaysAutoResize == "AlwaysAutoResize" then
            imgui.SetNextWindowSizeConstraints(0, 0, options.W, options.H)
        end

        if imgui.Begin(windowName,
            nil,
            {
                options.NoTitleBar,
                options.NoResize,
                options.NoMove,
                options.AlwaysAutoResize,
            }) then
            PresentScapes()

            lib_helpers.WindowPositionAndSize(windowName,
                options.X,
                options.Y,
                options.W,
                options.H,
                options.Anchor,
                options.AlwaysAutoResize,
                options.changed)
        end
        imgui.End()

        if options.TransparentWindow == true then
            imgui.PopStyleColor()
        end

        options.changed = false
    end
end

local function init()
    ConfigurationWindow = cfg.ConfigurationWindow(options)

    local function mainMenuButtonHandler()
        ConfigurationWindow.open = not ConfigurationWindow.open
    end

    core_mainmenu.add_button("Consumable Reader", mainMenuButtonHandler)

    return
    {
        name = "Consumable Reader",
        version = "2.0.0",
        author = "MarcherTech & Lilyzavoqth",
        description = "Consumable Count",
        present = present,
    }
end

return
{
    __addon =
    {
        init = init
    }
}
