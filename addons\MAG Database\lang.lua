-- language data
-- mostly strings which are repeated numerous times
return {
  ["is_1st_tier"] = " is a first evolution MAG in Phantasy Star Online.",
  
  ["is_2nd_tier"] = " is a second evolution MAG in Phantasy Star Online.",
  
  ["is_3rd_tier"] = " is a third evolution MAG in Phantasy Star Online.",
  ["become_3rd_tier"] = "A second or third tier MAG can become another third tier form when it is a level divisible by 5 and at least level 50 after feeding, but does not meet the requirements to evolve into any specific fourth tier form. ",
  
  ["is_4th_tier"] = " is a fourth evolution MAG in Phantasy Star Online. Fourth evolution MAGs are only available in Episode I & II and later versions of the game, and are displayed as rare items.",
  ["become_4th_tier"] = "A second or third tier MAG can become a fourth tier form when it is a level divisible by 10 and at least level 100 after feeding.",
  
  ["only_whole_levels"] = "Only whole DEF, POW, DEX and MIND levels count for these conditions; the 0%%-100%% progress meters are disregarded.",
  ["class_is_disregarded"] = " at level 10+, the class of its holder at level 35+ is disregarded for the second evolution.",
  
  ["reaches_lv50"] = " reaches a level divisible by 5, and at least level 50, it becomes a third evolution MAG depending on its stats and on the class and section ID of its holder at that time.",
  ["can_continue_evolution"] = " can continue to evolve into other third tier forms whenever it is a level divisible by 5 after a feeding and the above stat, class, or section ID conditions have changed. In Episode I & II and later versions, when it reaches level 100, it may also evolve into a fourth tier form if the requirements are met.",
  ["no_evolution"] = "Fourth evolution MAGs will not evolve further.",
  
  ["mag_cell"] = " can be acquired by feeding a Tier 3 or lower mag ",
  ["mag_cell_info"] = "EP 1 & 2 mag cells can be found from holiday rappies during certain special events/periods. Haloween mag cells can also be found from pumpkins from the Jack O' Lantern Quest. EP 1 & 2 mag cells cannot be used on mags that have previously been created using another mag cell or any 4th evolution mags including new EP 1 & 2 special mags." ..
  "\n\nFor a tier 3 or lower mag to become a special mag, you must have a mag equipped that meets the evolution conditions and use the mag cell from your inventory.",
  ["cell_src"] = " can be acquired from :",
  
  ["mag_xmas"] = "  1. Holiday Rappy : Saint Rappy/Christmas Present.",
  ["mag_halloween"] = "  1. Holiday Rappy : Hallo Rappy/Jack O' Lantern.",
  ["mag_1_ticket"] = "  2. 1 Item ticket.",
  
  -- labels used in evolution conditions
      ["l_mag"] = "       MAG : ",
      ["l_lvl"] = "     Level : ",
    ["l_class"] = "     Class : ",
      ["l_sex"] = "    Gender : ",
    ["l_secid"] = "Section ID : ",
    ["l_stats"] = " MAG Stats : ",
     ["l_cell"] = "  MAG Cell : ",
  ["l_special"] = "   Special : ",
     ["l_pb"] = "   Photon Blast : ",
    ["l_act"] = "Activation Rate : ",
   ["l_full"] = "     Full Meter : ",
   ["l_boss"] = "           Boss : ",
   ["l_10hp"] = "         10%% HP : ",
    ["l_0hp"] = "           0 HP : ",
       ["nl"] = "\n             ", -- newline with the same length as labels ; used for multiple conditions under one label
  
  -- photon blasts
  ["pb_my"] = "Mylla & Youlla",
  ["pb_golla"] = "Golla",
  ["pb_pilla"] = "Pilla",
  ["pb_estlla"] = "Estlla",
  ["pb_leilla"] = "Leilla",
  ["pb_farlla"] = "Farlla",
  
  -- abilities
  ["ab_nodam"] = "Invulnerability",
  ["ab_buff"] = "Shifta & Deband",
  ["ab_heal"] = "Resta",
  ["ab_revive"] = "Reverser"
}