// Generated by BreakPoint Software's Hex Workshop v4.20
//   http://www.hexworkshop.com
//   http://www.bpsoft.com
//
//  Source File: client.bin
//         Time: 10/10/2004 21:41
// Orig. Offset: 0 / 0x00000000
//       Length: 168 / 0x000000A8 (bytes)
unsigned char client[168] =
{
    0xFF, 0xC7, 0xFC, 0xE3, 0x06, 0x98, 0x86, 0x1F, 0x27, 0x6F, 0xE6, 0x81, 0xC8, 0x0F, 0x93, 0xBB, 
    0xED, 0x2D, 0x5B, 0x75, 0x6F, 0x9C, 0x5A, 0x17, 0x90, 0x68, 0x11, 0xDF, 0xBB, 0x55, 0xB4, 0xC9, 
    0x7C, 0x67, 0x96, 0x2E, 0xDD, 0x96, 0xF7, 0x8E, 0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 
    0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 
    0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 0x6E, 0x1A, 0xD6, 0x01, 0xCA, 0xB5, 0x33, 0xAD, 
    0x52, 0x49, 0x5D, 0x7D, 0xF0, 0xA6, 0xF0, 0x1D, 0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 
    0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 
    0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 
    0xF7, 0xA3, 0x1D, 0xA8, 0x08, 0x99, 0xBE, 0x40, 0x8C, 0x12, 0xB0, 0x1C, 0xC7, 0xBA, 0x11, 0xD6, 
    0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 
    0xCA, 0xA7, 0xFB, 0x4D, 0x02, 0x7C, 0x67, 0xA7, 
} ;