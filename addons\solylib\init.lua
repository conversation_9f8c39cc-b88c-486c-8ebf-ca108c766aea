local menu = require("solylib.menu")
local items = require("solylib.items.items")

local function present()
    menu.present()
    items.present()
end

local function init()
    return
    {
        name = "Soly Lib",
        version = "1.0.1",
        author = "Solybum",
        description = "Libraries for the addons",
        present = present,
    }
end

return
{
    __addon =
    {
        init = init
    }
}
