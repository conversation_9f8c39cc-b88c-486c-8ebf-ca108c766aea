// Map file definitions

const char* Forest1_Online_Maps[] = 
{
	"map\\map_forest01_00e.dat",
	"map\\map_forest01_01e.dat",
	"map\\map_forest01_02e.dat",
	"map\\map_forest01_03e.dat",
	"map\\map_forest01_04e.dat"
};

const char* Forest1_Offline_Maps[] = 
{
	"map\\map_forest01_00_offe.dat",
	"map\\map_forest01_02_offe.dat",
	"map\\map_forest01_04_offe.dat"
};

const char* Forest1_Offline_Objects[] = 
{
	"map\\map_forest01_00o.dat",
	"map\\map_forest01_02o.dat",
	"map\\map_forest01_04o.dat"
};

const char* Forest2_Online_Maps[] = 
{
	"map\\map_forest02_00e.dat",
	"map\\map_forest02_01e.dat",
	"map\\map_forest02_02e.dat",
	"map\\map_forest02_03e.dat",
	"map\\map_forest02_04e.dat"
};

const char* Forest2_Offline_Maps[] = 
{
	"map\\map_forest02_00_offe.dat",
	"map\\map_forest02_03_offe.dat",
	"map\\map_forest02_04_offe.dat"
};

const char* Forest2_Offline_Objects[] = 
{
	"map\\map_forest02_00o.dat",
	"map\\map_forest02_03o.dat",
	"map\\map_forest02_04o.dat"
};


const char* Cave1_Online_Maps[] = {
	"map\\map_cave01_00_00e.dat",
	"map\\map_cave01_00_01e.dat",
	"map\\map_cave01_01_00e.dat",
	"map\\map_cave01_01_01e.dat",
	"map\\map_cave01_02_00e.dat",
	"map\\map_cave01_02_01e.dat"
};

const char* Cave1_Offline_Maps[] = 
{
	"map\\map_cave01_00_00_offe.dat",
	"map\\map_cave01_01_00_offe.dat",
	"map\\map_cave01_02_00_offe.dat",
};

const char* Cave1_Offline_Objects[] = 
{
	"map\\map_cave01_00_00o.dat",
	"map\\map_cave01_01_00o.dat",
	"map\\map_cave01_02_00o.dat",
};


const char* Cave2_Online_Maps[] = 
{
	"map\\map_cave02_00_00e.dat",
	"map\\map_cave02_00_01e.dat",
	"map\\map_cave02_01_00e.dat",
	"map\\map_cave02_01_01e.dat",
	"map\\map_cave02_02_00e.dat",
	"map\\map_cave02_02_01e.dat"
};

const char* Cave2_Offline_Maps[] = 
{
	"map\\map_cave02_00_00_offe.dat",
	"map\\map_cave02_01_00_offe.dat",
	"map\\map_cave02_02_00_offe.dat",
};


const char* Cave2_Offline_Objects[] = 
{
	"map\\map_cave02_00_00o.dat",
	"map\\map_cave02_01_00o.dat",
	"map\\map_cave02_02_00o.dat",
};


const char* Cave3_Online_Maps[] = 
{
	"map\\map_cave03_00_00e.dat",
	"map\\map_cave03_00_01e.dat",
	"map\\map_cave03_01_00e.dat",
	"map\\map_cave03_01_01e.dat",
	"map\\map_cave03_02_00e.dat",
	"map\\map_cave03_02_01e.dat"
};

const char* Cave3_Offline_Maps[] = 
{
	"map\\map_cave03_00_00_offe.dat",
	"map\\map_cave03_01_00_offe.dat",
	"map\\map_cave03_02_00_offe.dat",
};


const char* Cave3_Offline_Objects[] = 
{
	"map\\map_cave03_00_00o.dat",
	"map\\map_cave03_01_00o.dat",
	"map\\map_cave03_02_00o.dat",
};


const char* Mine1_Online_Maps[] = 
{
	"map\\map_machine01_00_00e.dat",
	"map\\map_machine01_00_01e.dat",
	"map\\map_machine01_01_00e.dat",
	"map\\map_machine01_01_01e.dat",
	"map\\map_machine01_02_00e.dat",
	"map\\map_machine01_02_01e.dat"
};

const char* Mine2_Online_Maps[] = 
{
	"map\\map_machine02_00_00e.dat",
	"map\\map_machine02_00_01e.dat",
	"map\\map_machine02_01_00e.dat",
	"map\\map_machine02_01_01e.dat",
	"map\\map_machine02_02_00e.dat",
	"map\\map_machine02_02_01e.dat"
};

const char* Ruins1_Online_Maps[] = 
{
	"map\\map_ancient01_00_00e.dat",
	"map\\map_ancient01_00_01e.dat",
	"map\\map_ancient01_01_00e.dat",
	"map\\map_ancient01_01_01e.dat",
	"map\\map_ancient01_02_00e.dat",
	"map\\map_ancient01_02_01e.dat"
};

const char* Ruins2_Online_Maps[] = 
{
	"map\\map_ancient02_00_00e.dat",
	"map\\map_ancient02_00_01e.dat",
	"map\\map_ancient02_01_00e.dat",
	"map\\map_ancient02_01_01e.dat",
	"map\\map_ancient02_02_00e.dat",
	"map\\map_ancient02_02_01e.dat"
};

const char* Ruins3_Online_Maps[] = 
{
	"map\\map_ancient03_00_00e.dat",
	"map\\map_ancient03_00_01e.dat",
	"map\\map_ancient03_01_00e.dat",
	"map\\map_ancient03_01_01e.dat",
	"map\\map_ancient03_02_00e.dat",
	"map\\map_ancient03_02_01e.dat"
};

const char* Temple1_Online_Maps[] = 
{
	"map\\map_ruins01_00_00e.dat",
	"map\\map_ruins01_01_00e.dat",
};

const char* Temple1_Offline_Maps[] = 
{
	"map\\map_ruins01_00_00_offe.dat",
	"map\\map_ruins01_01_00_offe.dat",
};

const char* Temple2_Online_Maps[] = 
{
	"map\\map_ruins02_00_00e.dat",
	"map\\map_ruins02_01_00e.dat",
};

const char* Temple2_Offline_Maps[] = 
{
	"map\\map_ruins02_00_00_offe.dat",
	"map\\map_ruins02_01_00_offe.dat",
};


const char* Spaceship1_Online_Maps[] = 
{
	"map\\map_space01_00_00e.dat",
	"map\\map_space01_01_00e.dat",
};

const char* Spaceship1_Offline_Maps[] = 
{
	"map\\map_space01_00_00_offe.dat",
	"map\\map_space01_01_00_offe.dat",
};

const char* Spaceship2_Online_Maps[] = 
{
	"map\\map_space02_00_00e.dat",
	"map\\map_space02_01_00e.dat",
};

const char* Spaceship2_Offline_Maps[] = 
{
	"map\\map_space02_00_00_offe.dat",
	"map\\map_space02_01_00_offe.dat",
};

const char* Jungle1_Online_Maps[] = 
{
	"map\\map_jungle01_00e.dat",
	"map\\map_jungle01_01e.dat",
	"map\\map_jungle01_02e.dat",
};

const char* Jungle1_Offline_Maps[] = 
{
	"map\\map_jungle01_00_offe.dat",
	"map\\map_jungle01_01_offe.dat",
	"map\\map_jungle01_02_offe.dat",
};

const char* Jungle2_Online_Maps[] = 
{
	"map\\map_jungle02_00e.dat",
	"map\\map_jungle02_01e.dat",
	"map\\map_jungle02_02e.dat",
};

const char* Jungle2_Offline_Maps[] = 
{
	"map\\map_jungle02_00_offe.dat",
	"map\\map_jungle02_01_offe.dat",
	"map\\map_jungle02_02_offe.dat",
};

const char* Jungle3_Online_Maps[] = 
{
	"map\\map_jungle03_00e.dat",
	"map\\map_jungle03_01e.dat",
	"map\\map_jungle03_02e.dat",
};

const char* Jungle3_Offline_Maps[] = 
{
	"map\\map_jungle03_00_offe.dat",
	"map\\map_jungle03_01_offe.dat",
	"map\\map_jungle03_02_offe.dat",
};

const char* Jungle4_Online_Maps[] = 
{
	"map\\map_jungle04_00_00e.dat",
	"map\\map_jungle04_00_01e.dat",
	"map\\map_jungle04_01_00e.dat",
	"map\\map_jungle04_01_01e.dat",
};

const char* Jungle4_Offline_Maps[] = 
{
	"map\\map_jungle04_00_00_offe.dat",
	"map\\map_jungle04_00_01_offe.dat",
	"map\\map_jungle04_01_00_offe.dat",
	"map\\map_jungle04_01_01_offe.dat",
};

const char* Jungle5_Online_Maps[] = 
{
	"map\\map_jungle05_00e.dat",
	"map\\map_jungle05_01e.dat",
	"map\\map_jungle05_02e.dat",
};

const char* Jungle5_Offline_Maps[] = 
{
	"map\\map_jungle05_00_offe.dat",
	"map\\map_jungle05_01_offe.dat",
	"map\\map_jungle05_02_offe.dat",
};

const char* Seabed1_Online_Maps[] = 
{
	"map\\map_seabed01_00_00e.dat",
	"map\\map_seabed01_00_01e.dat",
	"map\\map_seabed01_01_00e.dat",
	"map\\map_seabed01_01_01e.dat",
};

const char* Seabed1_Offline_Maps[] = 
{
	"map\\map_seabed01_00_00_offe.dat",
	"map\\map_seabed01_01_00_offe.dat",
};

const char* Seabed2_Online_Maps[] = 
{
	"map\\map_seabed02_00_00e.dat",
	"map\\map_seabed02_00_01e.dat",
	"map\\map_seabed02_01_00e.dat",
	"map\\map_seabed02_01_01e.dat",
};


const char* Seabed2_Offline_Maps[] = 
{
	"map\\map_seabed02_00_00_offe.dat",
	"map\\map_seabed02_01_00_offe.dat",
};

const char* Crater_East_Online_Maps[] = 
{
	"map\\map_wilds01_00_00e.dat",
	"map\\map_wilds01_00_01e.dat",
	"map\\map_wilds01_00_02e.dat",
};

const char* Crater_West_Online_Maps[] = 
{
	"map\\map_wilds01_01_00e.dat",
	"map\\map_wilds01_01_01e.dat",
	"map\\map_wilds01_01_02e.dat",
};

const char* Crater_South_Online_Maps[] = 
{
	"map\\map_wilds01_02_00e.dat",
	"map\\map_wilds01_02_01e.dat",
	"map\\map_wilds01_02_02e.dat",
};

const char* Crater_North_Online_Maps[] = 
{
	"map\\map_wilds01_03_00e.dat",
	"map\\map_wilds01_03_01e.dat",
	"map\\map_wilds01_03_02e.dat",
};

const char* Crater_Interior_Online_Maps[] = 
{
	"map\\map_crater01_00_00e.dat",
	"map\\map_crater01_00_01e.dat",
	"map\\map_crater01_00_02e.dat",
};

const char* Desert1_Online_Maps[] = 
{
	"map\\map_desert01_00_00e.dat",
	"map\\map_desert01_01_00e.dat",
	"map\\map_desert01_02_00e.dat",
};

const char* Desert2_Online_Maps[] = 
{
	"map\\map_desert02_00_00e.dat",
	"map\\map_desert02_00_01e.dat",
	"map\\map_desert02_00_02e.dat",
};

const char* Desert3_Online_Maps[] = 
{
	"map\\map_desert03_00_00e.dat",
	"map\\map_desert03_01_00e.dat",
	"map\\map_desert03_02_00e.dat",
};