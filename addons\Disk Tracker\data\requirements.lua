local requirements = {}

local FO_tech_limit = {
    <PERSON><PERSON><PERSON> = 30, <PERSON><PERSON><PERSON><PERSON> = 30, <PERSON><PERSON><PERSON> = 30,
    <PERSON><PERSON> = 30, <PERSON><PERSON><PERSON> = 30, <PERSON><PERSON><PERSON> = 30,
    <PERSON><PERSON><PERSON> = 30, <PERSON><PERSON><PERSON><PERSON> = 30, <PERSON><PERSON><PERSON> = 30,
    <PERSON><PERSON> = 30, <PERSON><PERSON> = 30,
    <PERSON><PERSON> = 30, <PERSON> = 7, <PERSON><PERSON><PERSON> = 1,
    <PERSON><PERSON><PERSON> = 30, <PERSON><PERSON> = 30,
    <PERSON><PERSON> = 30, <PERSON><PERSON><PERSON> = 30,
    <PERSON><PERSON><PERSON> = 1,
}

local f_tech_limit = {
    <PERSON>oie = 20, <PERSON><PERSON><PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
    <PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
    <PERSON><PERSON><PERSON> = 20, <PERSON><PERSON><PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
    <PERSON><PERSON> = 0, <PERSON><PERSON> = 0,
    <PERSON><PERSON> = 20, <PERSON> = 7, <PERSON><PERSON><PERSON> = 0,
    <PERSON><PERSON><PERSON> = 20, <PERSON><PERSON> = 20,
    <PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
    <PERSON><PERSON><PERSON> = 1,
}

local m_tech_limit = {
    Foie = 15, Gifoi<PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
    <PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
    <PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
    <PERSON><PERSON> = 0, <PERSON><PERSON> = 0,
    <PERSON><PERSON> = 15, <PERSON> = 5, <PERSON><PERSON><PERSON> = 0,
    <PERSON><PERSON><PERSON> = 0, <PERSON><PERSON> = 0,
    <PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
    <PERSON><PERSON><PERSON> = 1,
}

requirements.tech_limit = {
    HUmar     = m_tech_limit,
    HUnewearl = f_tech_limit,
    RAmar     = m_tech_limit,
    RAmarl    = f_tech_limit,
    FOmar     = FO_tech_limit,
    FOmarl    = FO_tech_limit,
    FOnewm    = FO_tech_limit,
    FOnewearl = FO_tech_limit,
}

--                 1,   2,   3,   4,   5,   6,   7,   8,   9,  10,  11,  12,  13,  14,  15,  16,  17,  18,  19,  20,  21,  22,  23,  24,  25,  26,  27,  28,  29,  30
requirements.MST = {
    Foie     = {  40,  60,  80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 540, 560, 580, 600, 620 },
    Zonde    = {  35,  60,  85, 110, 135, 160, 185, 210, 235, 260, 285, 310, 335, 360, 385, 410, 435, 460, 485, 510, 535, 560, 585, 610, 635, 660, 685, 710, 735, 760 },
    Barta    = {  44,  68,  92, 116, 140, 164, 188, 212, 236, 260, 284, 308, 332, 356, 380, 404, 428, 452, 476, 500, 524, 548, 572, 596, 620, 644, 668, 692, 716, 740 },
    Gifoie   = { 100, 125, 150, 175, 200, 225, 250, 275, 300, 325, 350, 375, 400, 425, 450, 475, 500, 525, 550, 575, 600, 625, 650, 675, 700, 725, 750, 775, 800, 825 },
    Gizonde  = { 100, 124, 148, 172, 196, 220, 244, 268, 292, 316, 340, 364, 388, 412, 436, 460, 484, 508, 532, 556, 580, 604, 628, 652, 676, 700, 724, 748, 772, 796 },
    Gibarta  = { 100, 125, 150, 175, 200, 225, 250, 275, 300, 325, 350, 375, 400, 425, 450, 475, 500, 525, 550, 575, 600, 625, 650, 675, 700, 725, 750, 775, 800, 825 },
    Rafoie   = { 133, 161, 189, 217, 245, 273, 301, 329, 357, 385, 413, 441, 469, 497, 525, 553, 581, 609, 637, 665, 693, 721, 749, 777, 805, 833, 861, 889, 917, 945 },
    Razonde  = { 106, 136, 166, 196, 226, 256, 286, 316, 346, 376, 406, 436, 466, 496, 526, 556, 586, 616, 646, 676, 706, 736, 766, 796, 826, 856, 886, 916, 946, 976 },
    Rabarta  = { 134, 164, 194, 224, 254, 284, 314, 344, 374, 404, 434, 464, 494, 524, 554, 584, 614, 644, 674, 704, 734, 764, 794, 824, 854, 884, 914, 944, 974, 1004 },
    Grants   = { 160, 188, 216, 244, 272, 300, 328, 356, 384, 412, 440, 468, 496, 524, 552, 580, 608, 636, 664, 692, 720, 748, 776, 804, 832, 860, 888, 916, 944, 1030 },
    Megid    = { 160, 188, 216, 244, 272, 300, 328, 356, 384, 412, 440, 468, 496, 524, 552, 580, 608, 636, 664, 692, 720, 748, 776, 804, 832, 860, 888, 916, 944, 972 },
    Resta    = {  50,  80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590, 620, 650, 680, 710, 740, 770, 800, 830, 860, 890, 920 },
    Anti     = {  85, 111, 137, 163, 189, 215, 241 },
    Shifta   = {  60,  88, 116, 144, 172, 200, 228, 256, 284, 312, 340, 368, 396, 424, 452, 480, 508, 536, 564, 592, 620, 648, 676, 704, 732, 760, 788, 816, 844, 872 },
    Deband   = {  60,  88, 116, 144, 172, 200, 228, 256, 284, 312, 340, 368, 396, 424, 452, 480, 508, 536, 564, 592, 620, 648, 676, 704, 732, 760, 788, 816, 844, 872 },
    Jellen   = {  60,  88, 116, 144, 172, 200, 228, 256, 284, 312, 340, 368, 396, 424, 452, 480, 508, 536, 564, 592, 620, 648, 676, 704, 732, 760, 788, 816, 844, 872 },
    Zalure   = {  60,  88, 116, 144, 172, 200, 228, 256, 284, 312, 340, 368, 396, 424, 452, 480, 508, 536, 564, 592, 620, 648, 676, 704, 732, 760, 788, 816, 844, 872 },
    Reverser = { 150 },
    Ryuker   = { 150 },
}

return requirements
