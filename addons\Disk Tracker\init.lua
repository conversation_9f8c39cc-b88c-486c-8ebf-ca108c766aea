local core_mainmenu = require("core_mainmenu")
local lib_helpers = require("solylib.helpers")
local lib_menu = require("solylib.menu")
local lib_characters = require("solylib.characters")
local lib_unitxt = require("solylib.unitxt")
local psointernal = require("psointernal")
local requirements = require("Disk Tracker.data.requirements")
local cfg = require("Disk Tracker.configuration")
local optionsLoaded, options = pcall(require, "Disk Tracker.options")
local optionsFileName = "addons/Disk Tracker/options.lua"
local ConfigurationWindow
local Frame
local Disks = {}
local TechniqueOrderAndColors = {
    { name = "Foie", color = 0xFFFF7634 },
    { name = "Zonde", color = 0xFFEFEE00 },
    { name = "Bart<PERSON>", color = 0xFF31CBFF },
    { divider = true },
    { name = "Gifoie", color = 0xFFFF7634 },
    { name = "Gizonde", color = 0xFFEFEE00 },
    { name = "<PERSON><PERSON><PERSON>", color = 0xFF31CBFF },
    { divider = true },
    { name = "Rafoie", color = 0xFFFF7634 },
    { name = "Razon<PERSON>", color = 0xFFEFEE00 },
    { name = "Rabarta", color = 0xFF31CBFF },
    { name = "Grants", color = 0xFFFFFFFF },
    { name = "Megid", color = 0xFFCB11FF },
    { divider = true },
    { name = "Resta", color = 0xFF00FFBD },
    { name = "Anti", color = 0xFF00FFBD },
    { name = "Reverser", color = 0xFF00FFBD },
    { divider = true },
    { name = "Shifta", color = 0xFFFF2031 },
    { name = "Deband", color = 0xFF0065FF },
    { name = "Jellen", color = 0xFFFF2031 },
    { name = "Zalure", color = 0xFF0065FF },
    { divider = true },
    { name = "Ryuker", color = 0xFF9455F7 },
}
local TechniqueOrderByElement = {
    { name = "Foie", color = 0xFFFF7634 },
    { name = "Gifoie", color = 0xFFFF7634 },
    { name = "Rafoie", color = 0xFFFF7634 },
    { divider = true },
    { name = "Zonde", color = 0xFFEFEE00 },
    { name = "Gizonde", color = 0xFFEFEE00 },
    { name = "Razonde", color = 0xFFEFEE00 },
    { divider = true },
    { name = "Barta", color = 0xFF31CBFF },
    { name = "Gibarta", color = 0xFF31CBFF },
    { name = "Rabarta", color = 0xFF31CBFF },
    { divider = true },
    { name = "Grants", color = 0xFFFFFFFF },
    { name = "Megid", color = 0xFFCB11FF },
    { divider = true },
    { name = "Resta", color = 0xFF00FFBD },
    { name = "Anti", color = 0xFF00FFBD },
    { name = "Reverser", color = 0xFF00FFBD },
    { divider = true },
    { name = "Shifta", color = 0xFFFF2031 },
    { name = "Deband", color = 0xFF0065FF },
    { name = "Jellen", color = 0xFFFF2031 },
    { name = "Zalure", color = 0xFF0065FF },
    { divider = true },
    { name = "Ryuker", color = 0xFF9455F7 },
}
local ClassColors = {
    HUmar = 0xFFCB3200, HUnewearl = 0xFFCB3200, HUcast = 0xFFCB3200, HUcaseal = 0xFFCB3200,
    RAmar = 0xFF26B705, RAmarl = 0xFF26B705, RAcast = 0xFF26B705, RAcaseal = 0xFF26B705,
    FOmar = 0xFF8294FF, FOmarl = 0xFF8294FF, FOnewm = 0xFF8294FF, FOnewearl = 0xFF8294FF,
}
local SectionIDColors = {
    Viridia = 0xFF16C60C, Greenill = 0xFF16C60C, Skyly = 0xFF00BFFF, Bluefull = 0xFF0065FF,
    Purplenum = 0xFF9455F7, Pinkal = 0xFFF500BD, Redria = 0xFFFF2031, Oran = 0xFFFF7634,
    Yellowboze = 0xFFEFEE00, Whitill = 0xFFFFFFFF,
}

if optionsLoaded then
    -- If options loaded, make sure we have all those we need
    options.configurationEnableWindow = lib_helpers.NotNilOrDefault(options.configurationEnableWindow, true)
    options.EnableWindow = lib_helpers.NotNilOrDefault(options.EnableWindow, true)
    options.HideWhenSymbolChat = lib_helpers.NotNilOrDefault(options.HideWhenSymbolChat, false)
    options.HideWhenMenuUnavailable = lib_helpers.NotNilOrDefault(options.HideWhenMenuUnavailable, true)
    options.changed = lib_helpers.NotNilOrDefault(options.changed, true)
    options.Anchor = lib_helpers.NotNilOrDefault(options.Anchor, 1)
    options.X = lib_helpers.NotNilOrDefault(options.X, 50)
    options.Y = lib_helpers.NotNilOrDefault(options.Y, 50)
    options.W = lib_helpers.NotNilOrDefault(options.W, 500)
    options.H = lib_helpers.NotNilOrDefault(options.H, 500)
    options.NoTitleBar = lib_helpers.NotNilOrDefault(options.NoTitleBar, "")
    options.NoResize = lib_helpers.NotNilOrDefault(options.NoResize, "")
    options.NoMove = lib_helpers.NotNilOrDefault(options.NoMove, "")
    options.AlwaysAutoResize = lib_helpers.NotNilOrDefault(options.AlwaysAutoResize, "")
    options.TransparentWindow = lib_helpers.NotNilOrDefault(options.TransparentWindow, false)
    options.colorize = lib_helpers.NotNilOrDefault(options.colorize, true)
    options.groupByElement = lib_helpers.NotNilOrDefault(options.groupByElement, false)
    options.showAllLocations = lib_helpers.NotNilOrDefault(options.showAllLocations, false)
else
    options =
    {
        configurationEnableWindow = true,
        EnableWindow = true,
        HideWhenSymbolChat = false,
        HideWhenMenuUnavailable = true,
        changed = true,
        Anchor = 1,
        X = 50,
        Y = 50,
        W = 500,
        H = 500,
        NoTitleBar = "",
        NoResize = "",
        NoMove = "",
        AlwaysAutoResize = "",
        TransparentWindow = false,
        colorize = true,
        groupByElement = false,
        showAllLocations = false,
    }
end

local function SaveOptions(options)
    local file = io.open(optionsFileName, "w")
    if file ~= nil then
        io.output(file)

        io.write("return\n")
        io.write("{\n")
        io.write(string.format("    configurationEnableWindow = %s,\n", tostring(options.configurationEnableWindow)))
        io.write(string.format("    EnableWindow = %s,\n", tostring(options.EnableWindow)))
        io.write(string.format("    HideWhenSymbolChat = %s,\n", tostring(options.HideWhenSymbolChat)))
        io.write(string.format("    HideWhenMenuUnavailable = %s,\n", tostring(options.HideWhenMenuUnavailable)))
        io.write(string.format("    Anchor = %i,\n", options.Anchor))
        io.write(string.format("    X = %i,\n", options.X))
        io.write(string.format("    Y = %i,\n", options.Y))
        io.write(string.format("    W = %i,\n", options.W))
        io.write(string.format("    H = %i,\n", options.H))
        io.write(string.format("    NoTitleBar = \"%s\",\n", options.NoTitleBar))
        io.write(string.format("    NoResize = \"%s\",\n", options.NoResize))
        io.write(string.format("    NoMove = \"%s\",\n", options.NoMove))
        io.write(string.format("    AlwaysAutoResize = \"%s\",\n", options.AlwaysAutoResize))
        io.write(string.format("    TransparentWindow = %s,\n", tostring(options.TransparentWindow)))
        io.write(string.format("    colorize = %s,\n", tostring(options.colorize)))
        io.write(string.format("    groupByElement = %s,\n", tostring(options.groupByElement)))
        io.write(string.format("    showAllLocations = %s,\n", tostring(options.showAllLocations)))
        io.write("}\n")

        io.close(file)
    end
end

local function SaveCharacterData(player, MST, techniques)
    local charFileName = "addons/Disk Tracker/data/" .. player .. ".lua"
    local file = io.open(charFileName, "w")
    if file ~= nil then
        io.output(file)
        io.write("return\n")
        io.write("{\n")
        io.write(string.format("    MST = %i,\n", MST))
        io.write("    techniques = {\n")
        for tech, level in pairs(techniques) do
            io.write(string.format("        %s = %i,\n", tech, level))
        end
        io.write("    },\n")
        io.write("}\n")
        io.close(file)
    end
end

local function ParseTechniqueDisk(itemString)
    local techName, techLevel = string.match(itemString, "(.+) Lv(%d+)")
    if techName and techLevel then
        techName = string.gsub(techName, "^%s*(.-)%s*$", "%1")
        return techName, tonumber(techLevel)
    end
    return nil
end

local function AddOrUpdateDisk(disks, techName, techLevel, loc)
    if disks[techName] == nil then
        disks[techName] = {}
    end
    if disks[techName][techLevel] == nil then
        disks[techName][techLevel] = { B = false, S = false, I = false }
    end
    disks[techName][techLevel][loc] = true
end

local function Log(message)
    table.insert(psointernal.log_items, { os.time(), "[Disk Tracker] " .. message })
end

local function ReadDiskData()
    Log("Reading disk data...")
    package.loaded["Backpack.data.chars"] = nil
    local charsLoaded, chars = pcall(require, "Backpack.data.chars")
    if not charsLoaded or chars == nil then
        Log("Error: Could not load Backpack/data/chars.lua. Is the Backpack addon installed and has it been run at least once?")
        Log("pcall result: " .. tostring(charsLoaded))
        Log("chars content: " .. tostring(chars))
        return
    end

    local newDisks = {}

    for charName, _ in pairs(chars) do
        newDisks[charName] = {}
    end

    package.loaded["Backpack.data.shared_bank"] = nil
    local sharedBankLoaded, sharedBank = pcall(require, "Backpack.data.shared_bank")
    if not sharedBankLoaded or sharedBank == nil then
        Log("Warning: Could not load Backpack/data/shared_bank.lua.")
    else
        Log("shared_bank.lua content: " .. tostring(sharedBank))
        for _, itemStr in pairs(sharedBank) do
            local techName, techLevel = ParseTechniqueDisk(itemStr)
            if techName then
                Log("Found disk in shared bank: " .. techName .. " Lv" .. techLevel)
                for charName, _ in pairs(chars) do
                    AddOrUpdateDisk(newDisks[charName], techName, techLevel, "S")
                end
            end
        end
    end

    for charName, _ in pairs(chars) do
        local bankFile = "Backpack.data." .. charName .. "_bank"
        package.loaded[bankFile] = nil
        local bankLoaded, bank = pcall(require, bankFile)
        if not bankLoaded or bank == nil then
            Log("Warning: Could not load " .. bankFile .. ".lua.")
        else
            Log(bankFile .. ".lua content: " .. tostring(bank))
            for _, itemStr in pairs(bank) do
                local techName, techLevel = ParseTechniqueDisk(itemStr)
                if techName then
                    Log("Found disk in " .. charName .. " bank: " .. techName .. " Lv" .. techLevel)
                    AddOrUpdateDisk(newDisks[charName], techName, techLevel, "B")
                end
            end
        end

        local invFile = "Backpack.data." .. charName .. "_inv"
        package.loaded[invFile] = nil
        local invLoaded, inv = pcall(require, invFile)
        if not invLoaded or inv == nil then
            Log("Warning: Could not load " .. invFile .. ".lua.")
        else
            Log(invFile .. ".lua content: " .. tostring(inv))
            for _, itemStr in pairs(inv) do
                local techName, techLevel = ParseTechniqueDisk(itemStr)
                if techName then
                    Log("Found disk in " .. charName .. " inventory: " .. techName .. " Lv" .. techLevel)
                    AddOrUpdateDisk(newDisks[charName], techName, techLevel, "I")
                end
            end
        end
    end
    Disks = newDisks
    Log("Finished reading disk data.")
end

function SolyTools_DiskTracker_Refresh()
    ReadDiskData()
end

local function TrackCharacterData()
    local playerAddress = lib_characters.GetSelf()
    if playerAddress == 0 then return end

    local name = lib_characters.GetPlayerName(playerAddress)
    if name == nil or name == "" then return end

    local classId = lib_characters.GetPlayerClass(playerAddress)
    local className = lib_unitxt.GetClassName(classId)
    local sectionId = lib_characters.GetPlayerSectionID(playerAddress)
    local sectionIdName = lib_unitxt.GetSectionIDName(sectionId)

    local isAndroid = className == "HUcast" or className == "RAcast" or className == "RAcaseal" or className == "HUcaseal"
    if isAndroid then return end

    if Frame >= 30 then
        local MST = lib_characters.GetPlayerMST(playerAddress)
        local techniques = {}
        for techName, techId in pairs(lib_characters.Techniques) do
            local level = lib_characters.GetPlayerTechniqueLevel(playerAddress, techId)
            techniques[techName] = level
        end

        local char = tostring(name .. '~~~' .. className .. '~~~' .. sectionIdName)
        SaveCharacterData(char, MST, techniques)
        Frame = 0
    end
    Frame = Frame + 1
end

local function PresentDiskTracker()
    local characters = {}
    local charsLoaded, chars = pcall(require, "Backpack.data.chars")
    if charsLoaded and chars ~= nil then
        for charName, _ in pairs(chars) do
            table.insert(characters, charName)
        end
    end

    for _, charName in ipairs(characters) do
        local name, className, sectionIdName = string.match(charName, "(.+)~~~(.+)~~~(.+)")
        package.loaded["Disk Tracker.data." .. charName] = nil
        local charDataLoaded, charData = pcall(require, "Disk Tracker.data." .. charName)

        if charDataLoaded and charData ~= nil then
            local treeNodeOpen = false
            -- Draw the header manually to apply colors
            if imgui.TreeNodeEx("##" .. charName, 0) then
                treeNodeOpen = true
            end
            imgui.SameLine(0, 5)
            imgui.Text(name)
            imgui.SameLine()
            local classColor = ClassColors[className] or 0xFFFFFFFF
            local cClass = lib_helpers.GetColorAsFloats(classColor)
            imgui.TextColored(cClass.r, cClass.g, cClass.b, cClass.a, className)
            imgui.SameLine()
            local sectionColor = SectionIDColors[sectionIdName] or 0xFFFFFFFF
            local cSection = lib_helpers.GetColorAsFloats(sectionColor)
            imgui.TextColored(cSection.r, cSection.g, cSection.b, cSection.a, sectionIdName)

            if treeNodeOpen then
                local order = options.groupByElement and TechniqueOrderByElement or TechniqueOrderAndColors

                -- Determine max width for tech names for padding
                local maxTechNameWidth = 0
                local highestMaxLevel = 0
                for _, techInfo in ipairs(order) do
                    if techInfo.name then
                        if charData.techniques ~= nil then
                            local learnedLevel = charData.techniques[techInfo.name]
                            if learnedLevel ~= nil then
                                if requirements.tech_limit[className] ~= nil and requirements.tech_limit[className][techInfo.name] ~= nil then
                                    local maxLevel = requirements.tech_limit[className][techInfo.name]
                                    if learnedLevel < maxLevel then
                                        if #techInfo.name > maxTechNameWidth then
                                            maxTechNameWidth = #techInfo.name
                                        end
                                        if maxLevel > highestMaxLevel then
                                            highestMaxLevel = maxLevel
                                        end
                                    end
                                end
                            end
                        end
                    end
                end

                local anyTechShownInGroup = false
                for _, techInfo in ipairs(order) do
                    if techInfo.divider then
                        if anyTechShownInGroup then
                            imgui.Separator()
                        end
                        anyTechShownInGroup = false
                    else
                        local techName = techInfo.name
                        if charData.techniques ~= nil then
                            local learnedLevel = charData.techniques[techName]
                            if learnedLevel ~= nil then
                                if requirements.tech_limit[className] ~= nil and requirements.tech_limit[className][techName] ~= nil then
                                    local maxLevel = requirements.tech_limit[className][techName]
                                    if learnedLevel < maxLevel then
                                        anyTechShownInGroup = true
                                        local techColor = options.colorize and techInfo.color or 0xFFFFFFFF

                                        -- Tech Name
                                        local c = lib_helpers.GetColorAsFloats(techColor)
                                        imgui.TextColored(c.r, c.g, c.b, c.a, techName)
                                        imgui.SameLine(0, 0)

                                        -- Padding
                                        local paddingSize
                                        if highestMaxLevel < 10 then
                                            paddingSize = maxTechNameWidth - #techName + 1
                                        else
                                            if learnedLevel < 10 then
                                                paddingSize = maxTechNameWidth - #techName + 2
                                            else
                                                paddingSize = maxTechNameWidth - #techName + 1
                                            end
                                        end
                                        local padding = string.rep(" ", paddingSize)
                                        imgui.Text(padding)
                                        imgui.SameLine(0, 0)

                                        -- Level
                                        local levelString = tostring(learnedLevel)
                                        imgui.TextColored(c.r, c.g, c.b, c.a, levelString)
                                        imgui.SameLine()

                                        -- Disks
                                        local availableDiskLevels = {}
                                        if Disks[charName] and Disks[charName][techName] then
                                            for level, _ in pairs(Disks[charName][techName]) do
                                                table.insert(availableDiskLevels, tonumber(level))
                                            end
                                        end
                                        table.sort(availableDiskLevels)

                                        for _, level in ipairs(availableDiskLevels) do
                                            if level > learnedLevel and level <= maxLevel then
                                                local MSTReq = requirements.MST[techName][level]
                                                local diskLocations = Disks[charName][techName][level]

                                                local locString
                                                if options.showAllLocations then
                                                    locString = (diskLocations.B and "b" or "-") ..
                                                                (diskLocations.S and "s" or "-") ..
                                                                (diskLocations.I and "i" or "-")
                                                else
                                                    if diskLocations.B then locString = "b"
                                                    elseif diskLocations.S then locString = "s"
                                                    else locString = "i"
                                                    end
                                                end

                                                local diskLevelString = string.format("%d", level)
                                                local diskLocString = string.format(" %s", locString)

                                                local learnable = charData.MST >= MSTReq
                                                local levelColor = lib_helpers.GetColorAsFloats(techColor)
                                                local locColor = levelColor

                                                local levelWidth, levelHeight = imgui.CalcTextSize(diskLevelString)
                                                local locWidth, locHeight = imgui.CalcTextSize(diskLocString)
                                                local totalWidth = levelWidth + locWidth
                                                local maxHeight = math.max(levelHeight, locHeight)

                                                imgui.SameLine()

                                                if learnable then
                                                    local cHighlight = lib_helpers.GetColorAsFloats(0xFF004400) -- Darker Green
                                                    imgui.PushStyleColor("PlotHistogram", cHighlight.r, cHighlight.g, cHighlight.b, cHighlight.a)
                                                else
                                                    local cDarkGray = lib_helpers.GetColorAsFloats(0xFF202020)
                                                    imgui.PushStyleColor("FrameBg", cDarkGray.r, cDarkGray.g, cDarkGray.b, cDarkGray.a)
                                                end

                                                local cursorPosX = imgui.GetCursorPosX()
                                                local cursorPosY = imgui.GetCursorPosY()
                                                imgui.ProgressBar(learnable and 1.0 or 0.0, totalWidth, maxHeight, "")
                                                imgui.SetCursorPos(cursorPosX, cursorPosY)

                                                imgui.TextColored(levelColor.r, levelColor.g, levelColor.b, levelColor.a, diskLevelString)
                                                imgui.SameLine(0, 0)
                                                imgui.TextColored(locColor.r, locColor.g, locColor.b, locColor.a, diskLocString)

                                                imgui.PopStyleColor(1)

                                                imgui.SameLine()
                                            end
                                        end
                                        imgui.NewLine()
                                    end
                                end
                            end
                        end
                    end
                end
                imgui.TreePop()
            end
        end
    end
end


local function present()
    TrackCharacterData()
    -- If the addon has never been used, open the config window
    -- and disable the config window setting
    if options.configurationEnableWindow then
        ConfigurationWindow.open = true
        options.configurationEnableWindow = false
    end
    ConfigurationWindow.Update()

    if ConfigurationWindow.changed then
        ConfigurationWindow.changed = false
        SaveOptions(options)
    end

    if (options.EnableWindow == true)
            and (options.HideWhenSymbolChat == false or lib_menu.IsSymbolChatOpen() == false)
            and (options.HideWhenMenuUnavailable == false or lib_menu.IsMenuOpen() == true) then
        local windowName = "Disk Tracker"

        if options.TransparentWindow == true then
            imgui.PushStyleColor("WindowBg", 0.0, 0.0, 0.0, 0.0)
        end

        if options.AlwaysAutoResize ~= "AlwaysAutoResize" then
            imgui.SetNextWindowSizeConstraints(0, 0, options.W, options.H)
        end

        if imgui.Begin(windowName,
            nil,
            {
                options.NoTitleBar,
                options.NoResize,
                options.NoMove,
                options.AlwaysAutoResize,
            }) then
            PresentDiskTracker()

            lib_helpers.WindowPositionAndSize(windowName,
                options.X,
                options.Y,
                options.W,
                options.H,
                options.Anchor,
                options.AlwaysAutoResize,
                options.changed)
        end
        imgui.End()

        if options.TransparentWindow == true then
            imgui.PopStyleColor()
        end

        options.changed = false
    end
end

local function init()
    ConfigurationWindow = cfg.ConfigurationWindow(options)
    Frame = 0

    local function mainMenuButtonHandler()
        ConfigurationWindow.open = not ConfigurationWindow.open
    end

    core_mainmenu.add_button("Disk Tracker", mainMenuButtonHandler)
    lib_menu.register_callback(ReadDiskData)
    ReadDiskData()

    return
    {
        name = "Disk Tracker",
        version = "1.0.0",
        author = "Jules",
        description = "Tracks learned techniques and available technique disks.",
        present = present,
    }
end

return
{
    __addon =
    {
        init = init
    }
}
