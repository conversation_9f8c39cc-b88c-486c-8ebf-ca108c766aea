local core_mainmenu = require("core_mainmenu")
local lib_helpers = require("solylib.helpers")
local lib_characters = require("solylib.characters")
local lib_menu = require("solylib.menu")
local cfg = require("Player Scouter.configuration")

local optionsLoaded, options = pcall(require, "Player Scouter.options")
local optionsFileName = "addons/Player Scouter/options.lua"

local ConfigurationWindow

local origPackagePath = package.path
package.path = './addons/Player Scouter/lua-xtype/src/?.lua;' .. package.path
package.path = './addons/Player Scouter/MGL/src/?.lua;' .. package.path
local xtype = require("xtype")
local mgl = require("MGL")
package.path = origPackagePath

local function SetDefaultValue(Table, Index, Value)
    Table[Index] = lib_helpers.NotNilOrDefault(Table[Index], Value)
end

local function LoadOptions()
    if options == nil or type(options) ~= "table" then
        options = {}
    end
    SetDefaultValue(options, "configurationEnableWindow", true)
    SetDefaultValue(options, "enable", true)
    SetDefaultValue(options, "showName", true)
    SetDefaultValue(options, "showHpBar", true)
    SetDefaultValue(options, "showHpText", true)
    SetDefaultValue(options, "showBuffs", true)
    SetDefaultValue(options, "trackerYOffset", 7)
    SetDefaultValue(options, "minimumWidth", 16)
    SetDefaultValue(options, "transparentWindow", false)
    SetDefaultValue(options, "clampToView", true)
    SetDefaultValue(options, "clampBorderX", 10)
    SetDefaultValue(options, "clampBorderY", 10)
    SetDefaultValue(options, "hideWhenMenuOpen", true)
    SetDefaultValue(options, "hideWhenMenuUnavailable", true)
    SetDefaultValue(options, "hideWhenSymbolChatOpen", true)
end
LoadOptions()

local optionsStringBuilder = ""
local function BuildOptionsString(table, depth)
    local tabSpacing = 4
    local maxDepth = 5

    if not depth or depth == nil then
        depth = 0
    end
    local spaces = string.rep(" ", tabSpacing + tabSpacing * depth)

    if depth < 1 then
        optionsStringBuilder = "return\n{\n"
    end
    for key, value in pairs(table) do
        local vtype = type(value)
        if vtype == "string" then
            optionsStringBuilder = optionsStringBuilder .. spaces .. string.format("%s = \"%s\",\n", key, tostring(value))
        elseif vtype == "number" then
            if value % 1 == 0 then
                optionsStringBuilder = optionsStringBuilder .. spaces .. string.format("%s = %i,\n", key, tostring(value))
            else
                optionsStringBuilder = optionsStringBuilder .. spaces .. string.format("%s = %f,\n", key, tostring(value))
            end
        elseif vtype == "boolean" or value == nil then
            optionsStringBuilder = optionsStringBuilder .. spaces .. string.format("%s = %s,\n", key, tostring(value))
        elseif vtype == "table" then
            if maxDepth > 5 then
                return
            end
            optionsStringBuilder = optionsStringBuilder .. spaces .. string.format("%s = {\n", key)
            BuildOptionsString(value, depth + 1)
            optionsStringBuilder = optionsStringBuilder .. spaces .. string.format("},\n", key)
        end
    end
    if depth < 1 then
        optionsStringBuilder = optionsStringBuilder .. "}\n"
    end
end

local function SaveOptions(options)
    local file = io.open(optionsFileName, "w")
    if file ~= nil then
        BuildOptionsString(options)
        io.output(file)
        io.write(optionsStringBuilder)
        io.close(file)
    end
end

-- Camera related memory addresses
local _CameraPosX      = 0x00A48780
local _CameraPosY      = 0x00A48784
local _CameraPosZ      = 0x00A48788
local _CameraDirX      = 0x00A4878C
local _CameraDirY      = 0x00A48790
local _CameraDirZ      = 0x00A48794
local _CameraZoomLevel = 0x009ACEDC

local cameraCoords = nil
local cameraDirs = nil
local resolutionWidth = {}
local resolutionHeight = {}
local screenFov = nil
local aspectRatio = nil
local eyeWorld    = nil
local eyeDir      = nil
local determinantScr = nil
local cameraZoom = nil
local lastCameraZoom = nil

local function getCameraZoom()
    return pso.read_u32(_CameraZoomLevel)
end

local function getCameraCoordinates()
    return {
        x = pso.read_f32(_CameraPosX),
        y = pso.read_f32(_CameraPosY),
        z = pso.read_f32(_CameraPosZ),
    }
end

local function getCameraDirection()
    return {
        x = pso.read_f32(_CameraDirX),
        y = pso.read_f32(_CameraDirY),
        z = pso.read_f32(_CameraDirZ),
    }
end

local function clampVal(clamp, min, max)
    return clamp < min and min or clamp > max and max or clamp
end

local function calcScreenResolutions(forced)
    if forced or not resolutionWidth.val or not resolutionHeight.val then
        resolutionWidth.val          = lib_helpers.GetResolutionWidth()
        resolutionHeight.val         = lib_helpers.GetResolutionHeight()
        aspectRatio                      = resolutionWidth.val / resolutionHeight.val
        resolutionWidth.half             = resolutionWidth.val * 0.5
        resolutionHeight.half            = resolutionHeight.val * 0.5
    end
end

local function calcScreenFoV(forced)
    if not aspectRatio or not cameraZoom or not resolutionHeight.val then
        cameraZoom = getCameraZoom()
        calcScreenResolutions(forced)
    end

    if forced or cameraZoom ~= lastCameraZoom or cameraZoom == nil then
        screenFov = math.rad(
            math.deg(
                2 * math.atan(0.56470588 * aspectRatio)
            ) - (cameraZoom - 1) * 0.600 - clampVal(cameraZoom, 0, 1) * 0.300
        )
        determinantScr = aspectRatio * 3 * resolutionHeight.val / (6 * math.tan(0.5 * screenFov))
        lastCameraZoom = cameraZoom
    end
end

-- Projects a 3D world coordinate to a 2D screen coordinate.
local function computePixelCoordinates(pWorld, eyeWorld, eyeDir, determinant)
    local pRaster = mgl.vec2(0)
    local vis = -1
    local vDir = pWorld - eyeWorld
    vDir = mgl.normalize(vDir)
    local fdp = mgl.dot(eyeDir, vDir)
    if fdp == 0 then
        return pRaster, -1
    end
    local ddfp = determinant / fdp
    local vProj = mgl.vec3(ddfp, ddfp, ddfp) * vDir
    local eyeRight = mgl.cross(eyeDir, mgl.vec3(0, 1, 0))
    local eyeLeft = mgl.cross(eyeRight, eyeDir)
    if fdp > 0.0000001 then
        vis = 1
    end
    pRaster.x = mgl.dot(eyeRight, vProj)
    pRaster.y = -mgl.dot(eyeLeft, vProj)
    return pRaster, vis
end

-- Calculates the max duration of a Shifta/Deband buff based on its level.
local function getSDMaxDuration(level)
    return 30 + 10 * level
end

-- Draws a progress bar for a buff.
local function drawBuffBar(tech, color)
    local duration = getSDMaxDuration(tech.level)
    local current = tech.time
    local text = string.format("%s Lv%02i", tech.name, tech.level)
    lib_helpers.imguiProgressBar(true, current / duration, -1, imgui.GetFontSize(), color, nil, text)
end

-- Presents the UI for a single player.
local function PresentPlayer(player)
    if player.address == 0 then
        return
    end

    local name = lib_characters.GetPlayerName(player.address)
    -- Escape '%' in the name for older plugin versions.
    if pso.require_version == nil or not pso.require_version(3, 6, 0) then
        name = string.gsub(name, "%%", "%%%%")
    end
    local hp = lib_characters.GetPlayerHP(player.address)
    local mhp = lib_characters.GetPlayerMaxHP(player.address)
    local hpColor = lib_helpers.HPToGreenRedGradient(hp / mhp)
    local atkTech = lib_characters.GetPlayerTechniqueStatus(player.address, 0)
    local defTech = lib_characters.GetPlayerTechniqueStatus(player.address, 1)
    local invuln = lib_characters.GetPlayerInvulnerabilityStatus(player.address)

    if options.showName then
        local name_len = string.len(name)
        local min_width = options.minimumWidth or 0
        lib_helpers.Text(true, "%s", name)

        if name_len < min_width then
            local padding = string.rep("-", min_width - name_len)
            imgui.SameLine(0, 0)
            lib_helpers.TextC(false, 0x00FFFFFF, padding)
        end
    end

    if options.showHpBar and mhp > 0 then
        local hpText = nil
        local barHeight = imgui.GetFontSize()
        if options.showHpText then
            hpText = hp
        else
            barHeight = barHeight * 0.5
        end
        lib_helpers.imguiProgressBar(true, hp / mhp, -1, barHeight, hpColor, nil, hpText)
    end

    if options.showBuffs then
        if atkTech.type ~= 0 then
            drawBuffBar(atkTech, 0xFFFF0000)
        end
        if defTech.type ~= 0 then
            drawBuffBar(defTech, 0xFF0000FF)
        end
        if invuln.time > 0 then
            lib_helpers.Text(true, "%-4s: %s", "Inv.", os.date("!%M:%S", invuln.time))
        end
    end
end

-- Main render loop.
local function present()
    -- Handle configuration window opening/closing.
    if options.configurationEnableWindow then
        ConfigurationWindow.open = true
        options.configurationEnableWindow = false
    end

    ConfigurationWindow.Update()
    if ConfigurationWindow.changed then
        ConfigurationWindow.changed = false
        SaveOptions(options)
    end

    if not options.enable then
        return
    end

    if (options.hideWhenMenuOpen and lib_menu.IsMenuOpen()) or
       (options.hideWhenMenuUnavailable and lib_menu.IsMenuUnavailable()) or
       (options.hideWhenSymbolChatOpen and lib_menu.IsSymbolChatOpen()) then
        return
    end

    calcScreenResolutions()
    calcScreenFoV()

    local playerList = lib_characters.GetPlayerList()
    if not playerList then return end

    local self_addr = lib_characters.GetSelf()
    cameraCoords = getCameraCoordinates()
    cameraDirs = getCameraDirection()
    eyeWorld = mgl.vec3(cameraCoords.x, cameraCoords.y, cameraCoords.z)
    eyeDir = mgl.vec3(cameraDirs.x, cameraDirs.y, cameraDirs.z)

    local windowFlags = {"NoTitleBar", "NoResize", "NoMove", "NoScrollbar", "AlwaysAutoResize"}
    if options.transparentWindow then
        table.insert(windowFlags, "NoBackground")
    end

    for i = 1, #playerList do
        local player = playerList[i]
        -- Don't draw a scouter for the local player.
        if player.address ~= self_addr then
            local pCoords = {
                x = pso.read_f32(player.address + 0x38),
                y = pso.read_f32(player.address + 0x3C),
                z = pso.read_f32(player.address + 0x40),
            }

            -- Position the UI above the player's head.
            local pWorld = mgl.vec3(pCoords.x, pCoords.y + options.trackerYOffset, pCoords.z)
            local pRaster, visible = computePixelCoordinates(pWorld, eyeWorld, eyeDir, determinantScr)

            player.screenX = pRaster.x
            player.screenY = pRaster.y
            player.screenVisDirection = visible

            if options.clampToView then
                local rescaleX = resolutionWidth.half - options.clampBorderX
                local rescaleY = resolutionHeight.half - options.clampBorderY

                if player.screenVisDirection < 0 then
                    if math.abs(player.screenY) * rescaleX > math.abs(player.screenX) * rescaleY then
                        local factor = rescaleY / math.abs(player.screenY)
                        player.screenX = -player.screenX * factor
                        player.screenY = -player.screenY * factor
                    else
                        local factor = rescaleX / math.abs(player.screenX)
                        player.screenX = -player.screenX * factor
                        player.screenY = -player.screenY * factor
                    end
                else
                    if not (player.screenX > -rescaleX and player.screenX < rescaleX and
                            player.screenY > -rescaleY and player.screenY < rescaleY)
                    then
                        if math.abs(player.screenY) * rescaleX > math.abs(player.screenX) * rescaleY then
                            local factor = rescaleY / math.abs(player.screenY)
                            player.screenX = player.screenX * factor
                            player.screenY = player.screenY * factor
                        else
                            local factor = rescaleX / math.abs(player.screenX)
                            player.screenX = player.screenX * factor
                            player.screenY = player.screenY * factor
                        end
                    end
                end
                player.screenShow = true
            else
                if player.screenVisDirection < 0 then
                    player.screenShow = false
                else
                    player.screenShow = true
                end
            end

            if player.screenShow then
                local windowName = "Player Scouter - " .. player.index
                local sx = resolutionWidth.half + player.screenX
                local sy = resolutionHeight.half + player.screenY

                -- To correctly center the window, we need its size first.
                -- We can get it by creating a dummy window off-screen.
                local wx, wy
                imgui.SetNextWindowPos(-1000, -1000, "Always")
                if imgui.Begin("##PlayerScouterDummy" .. player.index, nil, windowFlags) then
                    PresentPlayer(player)
                    wx, wy = imgui.GetWindowSize()
                end
                imgui.End()

                local final_sx = sx - wx * 0.5
                local final_sy = sy - wy * 0.5
                imgui.SetNextWindowPos(final_sx, final_sy, "Always")
                imgui.SetNextWindowSize(wx, wy, "Always")

                if imgui.Begin(windowName, nil, windowFlags) then
                    PresentPlayer(player)
                end
                imgui.End()
            end
        end
    end
end

-- Addon initialization.
local function init()
    ConfigurationWindow = cfg.ConfigurationWindow(options)

    local function mainMenuButtonHandler()
        ConfigurationWindow.open = not ConfigurationWindow.open
    end

    core_mainmenu.add_button("Player Scouter", mainMenuButtonHandler)

    return {
        name = "Player Scouter",
        version = "1.1.0",
        author = "Jules",
        description = "Displays ally health bars and buff bars on-screen.",
        present = present,
    }
end

return {
    __addon = {
        init = init
    }
}
